//
//  DutyListViewModel.swift
//  DoctorDuty
//
//  Created by Augment Agent on 01/05/25.
//

import SwiftUI
import Combine

class DutyListViewModel: ObservableObject {
    // Main ViewModel reference
    private(set) var dutyViewModel: DutyViewModel

    // Published properties for UI updates
    @Published var selectedLocations: Set<String> = []
    @Published var showDayShifts: Bool = true
    @Published var showNightShifts: Bool = true
    @Published var showPaid: Bool = true
    @Published var showUnpaid: Bool = true
    @Published var searchText: String = ""

    // Computed property for available locations
    var availableLocations: [String] {
        let locations = dutyViewModel.getActiveLocations()
        return locations.sorted()
    }

    // Computed property for filtered duties
    var filteredDuties: [Duty] {
        var duties = dutyViewModel.sortedDuties()

        // Filter by search text
        if !searchText.isEmpty {
            duties = duties.filter { duty in
                duty.location.localizedCaseInsensitiveContains(searchText) ||
                (duty.notes?.localizedCaseInsensitiveContains(searchText) ?? false)
            }
        }

        // Filter by selected locations
        if !selectedLocations.isEmpty {
            duties = duties.filter { selectedLocations.contains($0.location) }
        }

        // Filter by shift type
        if showDayShifts && !showNightShifts {
            duties = duties.filter { !$0.isNightShift }
        } else if !showDayShifts && showNightShifts {
            duties = duties.filter { $0.isNightShift }
        } else if !showDayShifts && !showNightShifts {
            duties = []
        }

        // Filter by payment status
        if showPaid && !showUnpaid {
            duties = duties.filter { $0.isPaid }
        } else if !showPaid && showUnpaid {
            duties = duties.filter { !$0.isPaid }
        } else if !showPaid && !showUnpaid {
            duties = []
        }

        return duties
    }

    // Computed property for total filtered amount
    var totalFilteredAmount: Double {
        return filteredDuties.reduce(0) { $0 + $1.paymentAmount }
    }

    // Computed property for paid filtered amount
    var paidFilteredAmount: Double {
        return filteredDuties.filter { $0.isPaid }.reduce(0) { $0 + $1.paymentAmount }
    }

    // Computed property for unpaid filtered amount
    var unpaidFilteredAmount: Double {
        return filteredDuties.filter { !$0.isPaid }.reduce(0) { $0 + $1.paymentAmount }
    }

    // Initializer
    init(dutyViewModel: DutyViewModel) {
        self.dutyViewModel = dutyViewModel
    }

    /// Updates the reference to the DutyViewModel
    func updateDutyViewModel(_ viewModel: DutyViewModel) {
        self.dutyViewModel = viewModel
        objectWillChange.send()
    }

    // MARK: - Filter Actions

    /// Toggle selection of a location for filtering
    func toggleLocation(_ location: String) {
        if selectedLocations.contains(location) {
            selectedLocations.remove(location)
        } else {
            selectedLocations.insert(location)
        }
    }

    /// Clear all location filters
    func clearLocationFilters() {
        selectedLocations.removeAll()
    }

    /// Toggle day shift filter
    func toggleDayShifts() {
        showDayShifts.toggle()
    }

    /// Toggle night shift filter
    func toggleNightShifts() {
        showNightShifts.toggle()
    }

    /// Toggle paid duties filter
    func togglePaidDuties() {
        showPaid.toggle()
    }

    /// Toggle unpaid duties filter
    func toggleUnpaidDuties() {
        showUnpaid.toggle()
    }

    /// Reset all filters to default values
    func resetAllFilters() {
        selectedLocations.removeAll()
        showDayShifts = true
        showNightShifts = true
        showPaid = true
        showUnpaid = true
        searchText = ""
    }

    // MARK: - Duty Actions

    /// Delete duties at the specified offsets
    func deleteDuties(at offsets: IndexSet) {
        let dutiesToDelete = offsets.map { filteredDuties[$0] }

        for duty in dutiesToDelete {
            dutyViewModel.deleteDuty(duty)
        }
    }

    /// Toggle payment status for a duty
    func togglePaymentStatus(for duty: Duty) {
        dutyViewModel.togglePaymentStatus(for: duty)
    }

    /// Get color for a location
    func colorForLocation(_ location: String) -> Color {
        return dutyViewModel.colorForLocation(location)
    }

    /// Format currency value
    func formatCurrency(_ value: Double) -> String {
        return FormattingHelper.formatCurrency(value)
    }
}
