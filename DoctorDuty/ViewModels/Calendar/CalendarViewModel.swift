//
//  CalendarViewModel.swift
//  DoctorDuty
//
//  Created by Augment Agent on 01/05/25.
//

import SwiftUI
import Combine

class CalendarViewModel: ObservableObject {
    // Referência ao DutyViewModel principal
    private(set) var dutyViewModel: DutyViewModel

    // Propriedades publicadas para atualizações na UI
    @Published var selectedDate: Date = Date()
    @Published var currentMonth: Date = Date()
    @Published var isCalendarMinimized: Bool = false

    // Formatadores de data
    private let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        return formatter
    }()

    private let monthFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "MMMM yyyy"
        return formatter
    }()

    // Inicializador
    init(dutyViewModel: DutyViewModel) {
        self.dutyViewModel = dutyViewModel
    }

    // Método para atualizar a referência ao DutyViewModel
    func updateDutyViewModel(_ viewModel: DutyViewModel) {
        self.dutyViewModel = viewModel
        objectWillChange.send()
    }

    // MARK: - Métodos de Acesso

    /// Retorna os plantões para a data selecionada
    var dutiesForSelectedDate: [Duty] {
        return dutyViewModel.dutiesForDate(selectedDate)
    }

    /// Verifica se há plantões em uma data específica
    func hasDuties(on date: Date) -> Bool {
        return dutyViewModel.hasDuties(on: date)
    }

    /// Retorna o nome formatado do mês atual
    var currentMonthName: String {
        return monthFormatter.string(from: currentMonth)
    }

    /// Formata uma data para exibição
    func formatDate(_ date: Date) -> String {
        return dateFormatter.string(from: date)
    }

    // MARK: - Navegação do Calendário

    /// Avança para o próximo mês
    func nextMonth() {
        if let newDate = Calendar.current.date(byAdding: .month, value: 1, to: currentMonth) {
            currentMonth = newDate
        }
    }

    /// Retorna ao mês anterior
    func previousMonth() {
        if let newDate = Calendar.current.date(byAdding: .month, value: -1, to: currentMonth) {
            currentMonth = newDate
        }
    }

    /// Retorna ao mês atual
    func goToToday() {
        currentMonth = Date()
        selectedDate = Date()
    }

    // MARK: - Cálculos do Calendário

    /// Retorna os dias da semana como símbolos curtos
    var daysOfWeek: [String] {
        return dutyViewModel.daysOfWeek()
    }

    /// Retorna os dias do mês atual para exibição no calendário
    func daysInMonth() -> [Date?] {
        return dutyViewModel.daysInMonth(for: currentMonth)
    }

    /// Verifica se uma data é hoje
    func isToday(_ date: Date) -> Bool {
        return dutyViewModel.isToday(date)
    }

    /// Verifica se uma data está no mesmo dia que a data selecionada
    func isSelectedDate(_ date: Date) -> Bool {
        return dutyViewModel.isSameDay(date, selectedDate)
    }

    // MARK: - Ações do Usuário

    /// Seleciona uma data no calendário
    func selectDate(_ date: Date) {
        selectedDate = date
    }

    /// Alterna o estado minimizado do calendário
    func toggleCalendarMinimized() {
        isCalendarMinimized.toggle()
    }

    // MARK: - Gerenciamento de Plantões

    /// Adiciona um novo plantão
    func addDuty(_ duty: Duty) {
        dutyViewModel.addDuty(duty)
    }

    /// Atualiza um plantão existente
    func updateDuty(_ duty: Duty) {
        dutyViewModel.updateDuty(duty)
    }

    /// Exclui um plantão
    func deleteDuty(_ duty: Duty) {
        dutyViewModel.deleteDuty(duty)
    }

    /// Alterna o status de pagamento de um plantão
    func togglePaymentStatus(for duty: Duty) {
        dutyViewModel.togglePaymentStatus(for: duty)
    }

    /// Retorna a cor associada a um local
    func colorForLocation(_ location: String) -> Color {
        return dutyViewModel.colorForLocation(location)
    }
}
