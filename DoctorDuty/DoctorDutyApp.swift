//
//  DoctorDutyApp.swift
//  DoctorDuty
//
//  Created by <PERSON><PERSON><PERSON> on 01/05/25.
//

import SwiftUI
import UserNotifications

@main
struct DoctorDutyApp: App {
    @StateObject private var viewModel = DutyViewModel()
    @State private var notificationPermissionRequested = false

    init() {
        // Registrar para receber notificações
        requestNotificationPermission()
    }

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(viewModel)
                .onAppear {
                    if !notificationPermissionRequested {
                        requestNotificationPermission()
                    }
                }
        }
    }

    private func requestNotificationPermission() {
        NotificationService.shared.requestPermission { granted in
            notificationPermissionRequested = true
            if granted {
                print("Permissão para notificações concedida")
            } else {
                print("Permissão para notificações negada")
            }
        }
    }
}
