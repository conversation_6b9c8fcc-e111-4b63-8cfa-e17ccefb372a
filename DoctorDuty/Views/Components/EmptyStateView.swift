//
//  EmptyStateView.swift
//  DoctorDuty
//
//  Created by Augment Agent on 01/05/25.
//

import SwiftUI

struct EmptyStateView: View {
    enum EmptyStateType {
        case noDuties
        case noFilterResults
    }

    let type: EmptyStateType
    let action: () -> Void

    var body: some View {
        VStack(spacing: 20) {
            // Icon with background
            ZStack {
                Circle()
                    .fill(iconColor.opacity(0.1))
                    .frame(width: 120, height: 120)

                Image(systemName: iconName)
                    .font(.system(size: 60, weight: .light))
                    .foregroundColor(iconColor)
            }
            .padding(.bottom, 10)

            // Title
            Text(title)
                .font(.title2)
                .fontWeight(.bold)
                .multilineTextAlignment(.center)

            // Message
            Text(message)
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)
                .padding(.bottom, 10)
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    // Custom button style with scale effect
    struct ScaleButtonStyle: ButtonStyle {
        func makeBody(configuration: Configuration) -> some View {
            configuration.label
                .scaleEffect(configuration.isPressed ? 0.95 : 1)
                .animation(.spring(response: 0.3, dampingFraction: 0.6), value: configuration.isPressed)
        }
    }

    // Computed properties based on type
    private var iconName: String {
        switch type {
        case .noDuties:
            return "calendar.badge.plus"
        case .noFilterResults:
            return "slider.horizontal.3"
        }
    }

    private var iconColor: Color {
        switch type {
        case .noDuties:
            return .accentMedical
        case .noFilterResults:
            return Color(red: 0.95, green: 0.6, blue: 0.1) // Warmer orange
        }
    }

    private var title: String {
        switch type {
        case .noDuties:
            return "Nenhum Plantão Agendado"
        case .noFilterResults:
            return "Nenhum Resultado Encontrado"
        }
    }

    private var message: String {
        switch type {
        case .noDuties:
            return "Você ainda não tem nenhum plantão agendado. Comece a organizar sua agenda médica adicionando seu primeiro plantão."
        case .noFilterResults:
            return "Nenhum plantão corresponde aos filtros atuais. Tente ajustar ou remover alguns filtros para visualizar mais resultados."
        }
    }

    private var buttonTitle: String {
        switch type {
        case .noDuties:
            return "Adicionar Plantão"
        case .noFilterResults:
            return "Limpar Filtros"
        }
    }

    private var buttonIcon: String {
        switch type {
        case .noDuties:
            return "plus.circle.fill"
        case .noFilterResults:
            return "arrow.clockwise"
        }
    }
}

#Preview {
    VStack {
        EmptyStateView(type: .noDuties, action: {})
            .previewDisplayName("No Duties")

        EmptyStateView(type: .noFilterResults, action: {})
            .previewDisplayName("No Filter Results")
    }
}
