//
//  LocationFormView.swift
//  DoctorDuty
//
//  Created by Augment Agent on 01/05/25.
//

import SwiftUI

struct LocationFormView: View {
    @Environment(\.dismiss) private var dismiss
    @ObservedObject var viewModel: LocationViewModel

    // Cores predefinidas para escolha
    private let predefinedColors: [Color] = [
        .red, .orange, .yellow, .green, .blue, .indigo, .purple, .pink,
        Color(hex: "#4CAF50") ?? .green,  // Verde material
        Color(hex: "#2196F3") ?? .blue,   // Azul material
        Color(hex: "#9C27B0") ?? .purple, // Roxo material
        Color(hex: "#FF9800") ?? .orange, // Laranja material
        Color(hex: "#795548") ?? .brown,  // Marrom material
        Color(hex: "#607D8B") ?? .gray    // Azul acinzentado material
    ]

    var body: some View {
        NavigationStack {
            Form {
                Section(header: Text("Informações do Local")) {
                    TextField("Nome do Hospital/Clínica", text: $viewModel.locationName)

                    TextField("Endereço (opcional)", text: $viewModel.locationAddress)
                        .autocapitalization(.words)
                }

                Section(header: Text("Cor de Identificação")) {
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 10) {
                            ForEach(predefinedColors, id: \.self) { color in
                                Circle()
                                    .fill(color)
                                    .frame(width: 30, height: 30)
                                    .overlay(
                                        Circle()
                                            .stroke(Color.primary, lineWidth: viewModel.locationColor == color ? 2 : 0)
                                    )
                                    .onTapGesture {
                                        viewModel.locationColor = color
                                    }
                            }
                        }
                        .padding(.vertical, 8)
                    }

                    HStack {
                        Text("Cor selecionada:")
                        Spacer()
                        Circle()
                            .fill(viewModel.locationColor)
                            .frame(width: 24, height: 24)
                    }
                }

                Section {
                    Toggle("Local Ativo", isOn: $viewModel.locationIsActive)
                        .toggleStyle(SwitchToggleStyle(tint: .accentMedical))
                }

                if !viewModel.isFormValid {
                    Section {
                        Text("Por favor, preencha o nome do local")
                            .foregroundColor(.red)
                            .font(.caption)
                    }
                }
            }
            .navigationTitle(viewModel.formTitle)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancelar") {
                        viewModel.cancelOperation()
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(viewModel.actionButtonText) {
                        viewModel.saveLocation()
                        dismiss()
                    }
                    .disabled(!viewModel.isFormValid)
                }
            }
        }
    }
}

#Preview {
    let locationStore = LocationStore()
    let viewModel = LocationViewModel(locationStore: locationStore)
    viewModel.prepareForAddLocation()

    return LocationFormView(viewModel: viewModel)
}
