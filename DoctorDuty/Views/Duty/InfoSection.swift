import SwiftUI

struct InfoSection: View {
    @ObservedObject var formViewModel: DutyFormViewModel
    var body: some View {
        Section {
            VStack(spacing: 12) {
                HStack {
                    Image(systemName: "calendar.badge.clock")
                        .foregroundColor(.accentMedical)
                        .font(.system(size: 18))
                    Text("Informações do Plantão")
                        .font(.headline)
                        .foregroundColor(.primary)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Image(systemName: "calendar")
                            .foregroundColor(.accentMedical)
                            .font(.system(size: 14))
                        Text("Data")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    MiniCalendarView(selectedDate: $formViewModel.date)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }
                .padding(.vertical, 4)
                HStack(spacing: 12) {
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Image(systemName: "person.fill")
                                .foregroundColor(.accentMedical)
                                .font(.system(size: 14))
                            Text("Tipo de Pessoa")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                        Picker("", selection: $formViewModel.isLegalEntity) {
                            HStack {
                                Label("PF", image: "person.fill")
                            }.tag(false)
                            HStack {
                                Label("PJ", image: "building.2.fill")
                            }.tag(true)
                        }
                        .pickerStyle(.segmented)
                    }
                    .frame(maxWidth: .infinity)
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Image(systemName: "clock")
                                .foregroundColor(.accentMedical)
                                .font(.system(size: 14))
                            Text("Tipo de Plantão")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                        Picker("", selection: $formViewModel.isNightShift) {
                            HStack {
                                Text("Dia")
                            }.tag(false)
                            HStack {
                                Text("Noite")
                            }.tag(true)
                        }
                        .pickerStyle(.segmented)
                    }
                    .frame(maxWidth: .infinity)
                }
            }
            .padding(.vertical, 4)
        }
        .listSectionSeparator(.hidden)
    }
} 