//
//  Duty.swift
//  DoctorDuty
//
//  Created by Augment Agent on 01/05/25.
//

import Foundation

struct Duty: Identifiable, Codable {
    var id = UUID()
    var date: Date
    var location: String
    var paymentAmount: Double
    var isPaid: Bool
    var notes: String?

    // New fields
    var isLegalEntity: Bool // true = Pessoa Jurídica, false = Pessoa Física
    var isNightShift: Bool // true = Noturno, false = Diurno

    // Recurrence fields
    var isRecurring: Bool
    var recurrenceType: RecurrenceType?
    var recurrenceEndDate: Date?

    // Invoice reminder fields
    var needsInvoiceReminder: Bool
    var invoiceReminderDays: Int? // Dias após o plantão para lembrar de emitir a nota fiscal

    init(
        id: UUID = UUID(),
        date: Date,
        location: String,
        paymentAmount: Double,
        isPaid: Bool = false,
        notes: String? = nil,
        isLegalEntity: Bool = false,
        isNightShift: Bool = false,
        isRecurring: Bool = false,
        recurrenceType: RecurrenceType? = nil,
        recurrenceEndDate: Date? = nil,
        needsInvoiceReminder: Bool = false,
        invoiceReminderDays: Int? = nil
    ) {
        self.id = id
        self.date = date
        self.location = location
        self.paymentAmount = paymentAmount
        self.isPaid = isPaid
        self.notes = notes
        self.isLegalEntity = isLegalEntity
        self.isNightShift = isNightShift
        self.isRecurring = isRecurring
        self.recurrenceType = recurrenceType
        self.recurrenceEndDate = recurrenceEndDate
        self.needsInvoiceReminder = needsInvoiceReminder
        self.invoiceReminderDays = invoiceReminderDays
    }
}

enum RecurrenceType: String, Codable, CaseIterable {
    case daily = "Daily"
    case weekly = "Weekly"
    case biweekly = "Biweekly"
    case monthly = "Monthly"
}

extension Duty {
    static var sampleDuties: [Duty] {
        let today = Date()
        let calendar = Calendar.current
        var duties: [Duty] = []

        // Hospitais
        let hospitals = [
            "Hospital Santa Maria",
            "Hospital São Lucas",
            "Hospital Albert Einstein",
            "Hospital Sírio-Libanês",
            "Hospital Moinhos de Vento"
        ]

        // Valores base por hospital (alguns hospitais pagam mais que outros)
        let baseValues = [
            "Hospital Santa Maria": 800.0,
            "Hospital São Lucas": 950.0,
            "Hospital Albert Einstein": 1200.0,
            "Hospital Sírio-Libanês": 1300.0,
            "Hospital Moinhos de Vento": 1100.0
        ]

        // Gerar plantões para os últimos 24 meses (2 anos)
        for monthOffset in 0..<24 {
            // Calcular o mês atual (indo para trás a partir de hoje)
            guard let monthDate = calendar.date(byAdding: .month, value: -monthOffset, to: today) else {
                continue
            }

            // Determinar o ano e mês
            let year = calendar.component(.year, from: monthDate)
            let month = calendar.component(.month, from: monthDate)

            // Número de plantões por mês (variando para criar dados mais realistas)
            // Mais plantões nos meses mais recentes
            let dutiesCount = max(3, 10 - (monthOffset / 3))

            for i in 0..<dutiesCount {
                // Criar uma data dentro do mês
                var dateComponents = DateComponents()
                dateComponents.year = year
                dateComponents.month = month
                dateComponents.day = min(28, i + 1) // Evitar problemas com fevereiro

                guard let dutyDate = calendar.date(from: dateComponents) else {
                    continue
                }

                // Selecionar hospital aleatoriamente
                let hospital = hospitals[i % hospitals.count]

                // Valor base do hospital + variação por turno
                let baseValue = baseValues[hospital] ?? 900.0

                // Turno (noturno paga mais)
                let isNight = i % 2 == 0
                let nightBonus = isNight ? 200.0 : 0.0

                // Tipo de pessoa (PJ paga mais)
                let isLegal = i % 3 == 0
                let legalBonus = isLegal ? 150.0 : 0.0

                // Valor final
                let amount = baseValue + nightBonus + legalBonus

                // Status de pagamento (plantões mais antigos têm mais chance de estarem pagos)
                let isPaid = monthOffset > 3 ? (Double.random(in: 0...1) < 0.8) : (Double.random(in: 0...1) < 0.3)

                // Notas
                let notes = i % 5 == 0 ? "Plantão \(i+1) de \(calendar.monthSymbols[month-1])/\(year)" : nil

                // Recorrência (apenas alguns plantões são recorrentes)
                let isRecurring = i % 10 == 0
                let recurrenceType = isRecurring ? RecurrenceType.allCases[i % RecurrenceType.allCases.count] : nil
                let recurrenceEndDate = isRecurring ? calendar.date(byAdding: .month, value: 3, to: dutyDate) : nil

                let duty = Duty(
                    date: dutyDate,
                    location: hospital,
                    paymentAmount: amount,
                    isPaid: isPaid,
                    notes: notes,
                    isLegalEntity: isLegal,
                    isNightShift: isNight,
                    isRecurring: isRecurring,
                    recurrenceType: recurrenceType,
                    recurrenceEndDate: recurrenceEndDate
                )

                duties.append(duty)
            }
        }

        return duties.sorted(by: { $0.date < $1.date })
    }
}
