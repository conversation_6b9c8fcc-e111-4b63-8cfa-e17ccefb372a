# DoctorDuty

DoctorDuty is an iOS application for managing on-call medical shifts with features including a calendar, payment tracking, location tracking, and payment status.

## Project Structure

The project follows the MVVM (Model-View-ViewModel) architecture pattern and is organized as follows:

### Models

- `Duty.swift`: Represents a medical duty/shift with properties like date, location, payment amount, etc.
- `DutyStore.swift`: Manages the collection of duties and provides methods for CRUD operations.
- `Location.swift`: Represents a hospital or clinic location with properties like name, address, color, etc.
- `LocationStore.swift`: Manages the collection of locations and provides methods for CRUD operations.

### Views

#### Calendar
- `CalendarView.swift`: Main calendar view for visualizing duties by date.
- `MiniCalendarView.swift`: A compact calendar view for date selection.

#### Duty
- `DutyCard.swift`: A card component for displaying duty information in lists.
- `DutyDetailView.swift`: Detailed view of a single duty.
- `DutyFormView.swift`: Form for creating or editing a duty.
- `DutyListView.swift`: List view of all duties with filtering capabilities.
- `FilterView.swift`: View for selecting filters for the duty list.

#### Location
- `LocationFormView.swift`: Form for creating or editing a location.
- `LocationsListView.swift`: List view of all locations.

#### Settings
- `SettingsView.swift`: Application settings view.

#### Components
- `CurrencyTextField.swift`: Custom text field for currency input.
- `RightAlignedDecimalField.swift`: Custom text field for right-aligned decimal input.
- `EmptyStateView.swift`: Component for displaying empty state messages.

### ViewModels

#### Duty
- `DutyViewModel.swift`: Main view model for duty-related operations.
- `DutyFormViewModel.swift`: View model for the duty form.
- `DutyListViewModel.swift`: View model for the duty list with filtering capabilities.

### Extensions
- `Color+Extensions.swift`: Extensions for Color.
- `Date+Extensions.swift`: Extensions for Date.
- `Duty+Extensions.swift`: Extensions for Duty.
- `DutyViewModel+Extensions.swift`: Extensions for DutyViewModel.
- `String+Extensions.swift`: Extensions for String.

## Features

- Calendar view for visualizing duties
- Duty management (create, read, update, delete)
- Location management with color coding
- Payment tracking
- Filtering duties by hospital, shift type, and payment status

## Setup Instructions

1. Clone the repository
2. Open the project in Xcode
3. Build and run the application

## Note for Xcode Project

When opening this project in Xcode, you may need to update the project structure to match the file system structure. Follow these steps:

1. Open the project in Xcode
2. In the Project Navigator, create groups that match the folder structure:
   - Models
   - Views/Calendar
   - Views/Duty
   - Views/Location
   - Views/Settings
   - Views/Components
   - ViewModels/Duty
   - Extensions
3. Drag the files from the file system into the appropriate groups in Xcode

This will ensure that the Xcode project structure matches the file system structure.
