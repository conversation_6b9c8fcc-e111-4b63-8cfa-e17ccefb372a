//
//  RightAlignedDecimalField.swift
//  DoctorDuty
//
//  Created by Augment Agent on 01/05/25.
//

import SwiftUI
import Combine

struct RightAlignedDecimalField: View {
    @Binding var value: Double
    var placeholder: String
    
    @State private var text: String = ""
    @State private var isEditing = false
    
    init(value: Binding<Double>, placeholder: String = "0,00") {
        self._value = value
        self.placeholder = placeholder
        self._text = State(initialValue: Self.formatValue(value.wrappedValue))
    }
    
    var body: some View {
        ZStack(alignment: .trailing) {
            TextField(placeholder, text: $text, onEditingChanged: { editing in
                self.isEditing = editing
                if !editing {
                    // When editing ends, update the value
                    self.updateValueFromText()
                }
            })
            .keyboardType(.decimalPad)
            .multilineTextAlignment(.trailing)
            .onChange(of: text) { newValue, _ in
                // Filter out non-numeric characters except for comma and period
                let filtered = newValue.filter { "0123456789,.".contains($0) }
                if filtered != newValue {
                    text = filtered
                }
                
                // Ensure only one decimal separator
                if text.filter({ $0 == "," || $0 == "." }).count > 1 {
                    // Keep only the first decimal separator
                    var newText = ""
                    var foundSeparator = false
                    
                    for char in text {
                        if (char == "," || char == ".") && !foundSeparator {
                            newText.append(",") // Always use comma
                            foundSeparator = true
                        } else if char != "," && char != "." {
                            newText.append(char)
                        }
                    }
                    
                    text = newText
                }
                
                // Replace period with comma for consistency
                if text.contains(".") {
                    text = text.replacingOccurrences(of: ".", with: ",")
                }
                
                // Limit to 2 decimal places
                if let commaIndex = text.firstIndex(of: ",") {
                    let decimalPart = text[text.index(after: commaIndex)...]
                    if decimalPart.count > 2 {
                        let endIndex = text.index(commaIndex, offsetBy: 3)
                        text = String(text[..<endIndex])
                    }
                }
                
                // Update the value when text changes
                updateValueFromText()
            }
            .onChange(of: value) { newValue, _ in
                // Only update text if we're not currently editing
                if !isEditing {
                    text = Self.formatValue(newValue)
                }
            }
        }
    }
    
    private func updateValueFromText() {
        // Convert text to Double
        let processedText = text.replacingOccurrences(of: ",", with: ".")
        if let newValue = Double(processedText) {
            value = newValue
        }
    }
    
    private static func formatValue(_ value: Double) -> String {
        // Format with 2 decimal places
        return String(format: "%.2f", value).replacingOccurrences(of: ".", with: ",")
    }
}

#Preview {
    RightAlignedDecimalField(value: .constant(123.45))
        .padding()
        .previewLayout(.sizeThatFits)
}
