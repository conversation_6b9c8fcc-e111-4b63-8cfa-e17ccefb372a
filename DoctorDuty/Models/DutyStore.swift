//
//  DutyStore.swift
//  DoctorDuty
//
//  Created by Augment Agent on 01/05/25.
//

import Foundation
import SwiftUI

class DutyStore: ObservableObject {
    @Published var duties: [Duty] = []

    private static func fileURL() throws -> URL {
        try FileManager.default.url(for: .documentDirectory,
                                    in: .userDomainMask,
                                    appropriateFor: nil,
                                    create: false)
        .appendingPathComponent("duties.data")
    }

    /// Carrega os plantões do cache local
    func loadFromCache() async throws {
        let task = Task<[Duty], Error> {
            let fileURL = try Self.fileURL()
            guard let data = try? Data(contentsOf: fileURL) else {
                // Se não houver dados no cache, retorna um array vazio
                return []
            }
            let duties = try JSONDecoder().decode([Duty].self, from: data)
            return duties
        }

        let loadedDuties = try await task.value

        DispatchQueue.main.async {
            if !loadedDuties.isEmpty {
                self.duties = loadedDuties
            }
        }
    }

    /// Carrega os plantões do servidor (simulado)
    func loadFromServer() async throws {
        // Em um app real, aqui seria feita uma chamada de API para o servidor
        // Para fins de demonstração, estamos usando dados de exemplo

        let task = Task<[Duty], Error> {
            // Simula uma possível falha na rede (10% de chance)
            if Double.random(in: 0...1) < 0.1 {
                throw NSError(domain: "NetworkError", code: 500, userInfo: [NSLocalizedDescriptionKey: "Falha na conexão com o servidor"])
            }

            // Retorna os dados de exemplo
            return Duty.sampleDuties
        }

        let serverDuties = try await task.value

        DispatchQueue.main.async {
            // Mescla os dados do servidor com os dados do cache
            // Em um app real, você pode querer implementar uma lógica mais sofisticada
            // para resolver conflitos entre dados locais e do servidor

            // Abordagem simples: substitui os dados locais pelos do servidor
            self.duties = serverDuties
        }
    }

    /// Método legado para compatibilidade
    func load() async throws {
        // Primeiro tenta carregar do cache
        try await loadFromCache()

        // Se não houver dados no cache, carrega os dados de exemplo
        if duties.isEmpty {
            DispatchQueue.main.async {
                self.duties = Duty.sampleDuties
            }
        }
    }

    func save() async throws {
        let task = Task {
            let data = try JSONEncoder().encode(duties)
            let outfile = try Self.fileURL()
            try data.write(to: outfile)
        }
        _ = try await task.value
    }

    func add(_ duty: Duty) {
        duties.append(duty)
    }

    func update(_ duty: Duty) {
        if let index = duties.firstIndex(where: { $0.id == duty.id }) {
            duties[index] = duty
        }
    }

    func delete(_ duty: Duty) {
        if let index = duties.firstIndex(where: { $0.id == duty.id }) {
            duties.remove(at: index)
        }
    }

    func dutiesForDate(_ date: Date) -> [Duty] {
        let calendar = Calendar.current
        return duties.filter { duty in
            calendar.isDate(duty.date, inSameDayAs: date)
        }
    }
}
