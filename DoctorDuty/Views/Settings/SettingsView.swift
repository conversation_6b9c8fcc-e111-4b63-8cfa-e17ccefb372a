//
//  SettingsView.swift
//  DoctorDuty
//
//  Created by Augment Agent on 01/05/25.
//

import SwiftUI

struct SettingsView: View {
    @EnvironmentObject var dutyViewModel: DutyViewModel
    @EnvironmentObject var locationStore: LocationStore
    @StateObject private var viewModel: SettingsViewModel

    init() {
        // Inicializar com viewModels temporários que serão substituídos pelos @EnvironmentObject
        let tempDutyViewModel = DutyViewModel()
        let tempLocationStore = LocationStore()
        self._viewModel = StateObject(wrappedValue: SettingsViewModel(locationStore: tempLocationStore, dutyViewModel: tempDutyViewModel))
    }

    var body: some View {
        NavigationStack {
            List {
                Section(header: Text("Gerenciamento")) {
                    NavigationLink(destination: LocationsListView().environmentObject(locationStore)) {
                        HStack {
                            Image(systemName: "building.2")
                                .foregroundColor(.accentMedical)
                            Text("Hospitais e Clínicas")
                        }
                    }

                    NavigationLink(destination: PaymentSettingsView(viewModel: viewModel)) {
                        HStack {
                            Image(systemName: "dollarsign.circle")
                                .foregroundColor(.accentMedical)
                            Text("Configurações de Pagamento")
                        }
                    }
                }

                Section(header: Text("Notificações")) {
                    Toggle("Ativar Notificações", isOn: $viewModel.notificationsEnabled)
                        .toggleStyle(SwitchToggleStyle(tint: .accentMedical))

                    if viewModel.notificationsEnabled {
                        Picker("Tempo de Notificação", selection: $viewModel.notificationTime) {
                            ForEach(viewModel.notificationTimeOptions, id: \.value) { option in
                                Text(option.label).tag(option.value)
                            }
                        }
                    }
                }

                Section(header: Text("Aparência")) {
                    Toggle("Usar Tema do Sistema", isOn: $viewModel.useSystemTheme)
                        .toggleStyle(SwitchToggleStyle(tint: .accentMedical))

                    if !viewModel.useSystemTheme {
                        Toggle("Modo Escuro", isOn: $viewModel.useDarkMode)
                            .toggleStyle(SwitchToggleStyle(tint: .accentMedical))
                    }
                }

                Section(header: Text("Aplicativo")) {
                    Button(action: {
                        viewModel.showingAbout = true
                    }) {
                        HStack {
                            Image(systemName: "info.circle")
                                .foregroundColor(.accentMedical)
                            Text("Sobre o Aplicativo")
                            Spacer()
                            Image(systemName: "chevron.right")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }

                    Link(destination: URL(string: "https://example.com/support")!) {
                        HStack {
                            Image(systemName: "questionmark.circle")
                                .foregroundColor(.accentMedical)
                            Text("Suporte")
                            Spacer()
                            Image(systemName: "arrow.up.right.square")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }

                    Button(action: {
                        viewModel.resetSettings()
                    }) {
                        HStack {
                            Image(systemName: "arrow.counterclockwise")
                                .foregroundColor(.red)
                            Text("Restaurar Configurações Padrão")
                                .foregroundColor(.red)
                        }
                    }
                }

                Section(header: Text("Versão")) {
                    HStack {
                        Text("Versão do Aplicativo")
                        Spacer()
                        Text(viewModel.appVersion)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .navigationTitle("Configurações")
            .sheet(isPresented: $viewModel.showingAbout) {
                AboutView(viewModel: viewModel)
            }
            .sheet(isPresented: $viewModel.showingPaymentSettings) {
                PaymentSettingsView(viewModel: viewModel)
            }
            .onDisappear {
                viewModel.saveSettings()
            }
            .onAppear {
                // Atualizar o viewModel com os objetos corretos do ambiente
                if viewModel.dutyViewModel !== dutyViewModel || viewModel.locationStore !== locationStore {
                    viewModel.updateReferences(locationStore: locationStore, dutyViewModel: dutyViewModel)
                }
            }
        }
    }
}

struct AboutView: View {
    @Environment(\.dismiss) private var dismiss
    let viewModel: SettingsViewModel

    var body: some View {
        NavigationStack {
            VStack(spacing: 20) {
                Image(systemName: "stethoscope")
                    .font(.system(size: 60))
                    .foregroundColor(.accentMedical)

                Text("Doctor Duty")
                    .font(.largeTitle)
                    .fontWeight(.bold)

                Text("Versão \(viewModel.appVersion)")
                    .foregroundColor(.secondary)

                Divider()

                Text("Desenvolvido para ajudar médicos a gerenciar seus plantões, pagamentos e locais de trabalho de forma simples e eficiente.")
                    .multilineTextAlignment(.center)
                    .padding()

                Text("© \(viewModel.copyrightYear) Doctor Duty. Todos os direitos reservados.")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.top, 40)
            }
            .padding()
            .navigationTitle("Sobre")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Fechar") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct PaymentSettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @ObservedObject var viewModel: SettingsViewModel

    var body: some View {
        NavigationStack {
            Form {
                Section(header: Text("Valores Padrão")) {
                    HStack {
                        Text("Valor Padrão do Plantão")
                        Spacer()
                        TextField("Valor", value: $viewModel.defaultPaymentAmount, format: .currency(code: viewModel.defaultCurrency))
                            .keyboardType(.decimalPad)
                            .multilineTextAlignment(.trailing)
                            .frame(width: 120)
                    }

                    Toggle("Pessoa Jurídica por Padrão", isOn: $viewModel.defaultIsLegalEntity)
                        .toggleStyle(SwitchToggleStyle(tint: .accentMedical))
                }

                Section(header: Text("Moeda")) {
                    Picker("Moeda", selection: $viewModel.defaultCurrency) {
                        ForEach(viewModel.availableCurrencies, id: \.self) { currency in
                            Text(currency).tag(currency)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())

                    HStack {
                        Text("Símbolo da Moeda")
                        Spacer()
                        Text(viewModel.currencySymbol)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .navigationTitle("Configurações de Pagamento")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancelar") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Salvar") {
                        viewModel.saveSettings()
                        dismiss()
                    }
                    .fontWeight(.bold)
                }
            }
        }
    }
}

#Preview {
    let dutyViewModel = DutyViewModel()
    let locationStore = LocationStore()
    dutyViewModel.locationStore = locationStore

    return SettingsView()
        .environmentObject(dutyViewModel)
        .environmentObject(locationStore)
}
