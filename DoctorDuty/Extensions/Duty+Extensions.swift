//
//  Duty+Extensions.swift
//  DoctorDuty
//
//  Created by Augment Agent on 01/05/25.
//

import Foundation
import SwiftUI

extension Duty {
    // MARK: - Computed Properties
    
    /// Returns the type of person as a localized string
    var personTypeText: String {
        return isLegalEntity ? "Pessoa Jurídica" : "Pessoa Física"
    }
    
    /// Returns the short form of person type
    var personTypeShortText: String {
        return isLegalEntity ? "PJ" : "PF"
    }
    
    /// Returns the shift type as a localized string
    var shiftTypeText: String {
        return isNightShift ? "Noturno" : "Diurno"
    }
    
    /// Returns the payment status as a localized string
    var paymentStatusText: String {
        return isPaid ? "PAGO" : "PENDENTE"
    }
    
    /// Returns the background color for the person type
    var personTypeBackgroundColor: Color {
        return isLegalEntity ? Color.companyBackground : Color.individualBackground
    }
    
    /// Returns the background color for the shift type
    var shiftTypeBackgroundColor: Color {
        return isNightShift ? Color.nightShiftBackground : Color.dayShiftBackground
    }
    
    /// Returns the color for the payment status
    var paymentStatusColor: Color {
        return isPaid ? Color.paidColor : Color.unpaidColor
    }
    
    // MARK: - Validation Methods
    
    /// Validates if the duty has all required fields
    func isValid() -> Bool {
        return !location.isEmpty && paymentAmount > 0
    }
    
    /// Validates if the duty can be recurring
    func canBeRecurring() -> Bool {
        return isRecurring && recurrenceType != nil && recurrenceEndDate != nil
    }
}
