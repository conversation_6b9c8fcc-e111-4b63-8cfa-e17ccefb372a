// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		8AD7A7C02DC3A772008E616D /* Localizable.xcstrings in Resources */ = {isa = PBXBuildFile; fileRef = 8AD7A7BF2DC3A772008E616D /* Localizable.xcstrings */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		8AD7A7952DC3A459008E616D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 8AD7A77C2DC3A458008E616D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8AD7A7832DC3A458008E616D;
			remoteInfo = DoctorDuty;
		};
		8AD7A79F2DC3A459008E616D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 8AD7A77C2DC3A458008E616D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8AD7A7832DC3A458008E616D;
			remoteInfo = DoctorDuty;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		8AD7A7842DC3A458008E616D /* DoctorDuty.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = DoctorDuty.app; sourceTree = BUILT_PRODUCTS_DIR; };
		8AD7A7942DC3A459008E616D /* DoctorDutyTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = DoctorDutyTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		8AD7A79E2DC3A459008E616D /* DoctorDutyUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = DoctorDutyUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		8AD7A7BF2DC3A772008E616D /* Localizable.xcstrings */ = {isa = PBXFileReference; lastKnownFileType = text.json.xcstrings; path = Localizable.xcstrings; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		8AD7A7862DC3A458008E616D /* DoctorDuty */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = DoctorDuty;
			sourceTree = "<group>";
		};
		8AD7A7972DC3A459008E616D /* DoctorDutyTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = DoctorDutyTests;
			sourceTree = "<group>";
		};
		8AD7A7A12DC3A459008E616D /* DoctorDutyUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = DoctorDutyUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		8AD7A7812DC3A458008E616D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8AD7A7912DC3A459008E616D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8AD7A79B2DC3A459008E616D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		8AD7A77B2DC3A458008E616D = {
			isa = PBXGroup;
			children = (
				8AD7A7BF2DC3A772008E616D /* Localizable.xcstrings */,
				8AD7A7862DC3A458008E616D /* DoctorDuty */,
				8AD7A7972DC3A459008E616D /* DoctorDutyTests */,
				8AD7A7A12DC3A459008E616D /* DoctorDutyUITests */,
				8AD7A7852DC3A458008E616D /* Products */,
			);
			sourceTree = "<group>";
		};
		8AD7A7852DC3A458008E616D /* Products */ = {
			isa = PBXGroup;
			children = (
				8AD7A7842DC3A458008E616D /* DoctorDuty.app */,
				8AD7A7942DC3A459008E616D /* DoctorDutyTests.xctest */,
				8AD7A79E2DC3A459008E616D /* DoctorDutyUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		8AD7A7832DC3A458008E616D /* DoctorDuty */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8AD7A7A82DC3A459008E616D /* Build configuration list for PBXNativeTarget "DoctorDuty" */;
			buildPhases = (
				8AD7A7802DC3A458008E616D /* Sources */,
				8AD7A7812DC3A458008E616D /* Frameworks */,
				8AD7A7822DC3A458008E616D /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				8AD7A7862DC3A458008E616D /* DoctorDuty */,
			);
			name = DoctorDuty;
			packageProductDependencies = (
			);
			productName = DoctorDuty;
			productReference = 8AD7A7842DC3A458008E616D /* DoctorDuty.app */;
			productType = "com.apple.product-type.application";
		};
		8AD7A7932DC3A459008E616D /* DoctorDutyTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8AD7A7AB2DC3A459008E616D /* Build configuration list for PBXNativeTarget "DoctorDutyTests" */;
			buildPhases = (
				8AD7A7902DC3A459008E616D /* Sources */,
				8AD7A7912DC3A459008E616D /* Frameworks */,
				8AD7A7922DC3A459008E616D /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				8AD7A7962DC3A459008E616D /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				8AD7A7972DC3A459008E616D /* DoctorDutyTests */,
			);
			name = DoctorDutyTests;
			packageProductDependencies = (
			);
			productName = DoctorDutyTests;
			productReference = 8AD7A7942DC3A459008E616D /* DoctorDutyTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		8AD7A79D2DC3A459008E616D /* DoctorDutyUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8AD7A7AE2DC3A459008E616D /* Build configuration list for PBXNativeTarget "DoctorDutyUITests" */;
			buildPhases = (
				8AD7A79A2DC3A459008E616D /* Sources */,
				8AD7A79B2DC3A459008E616D /* Frameworks */,
				8AD7A79C2DC3A459008E616D /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				8AD7A7A02DC3A459008E616D /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				8AD7A7A12DC3A459008E616D /* DoctorDutyUITests */,
			);
			name = DoctorDutyUITests;
			packageProductDependencies = (
			);
			productName = DoctorDutyUITests;
			productReference = 8AD7A79E2DC3A459008E616D /* DoctorDutyUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		8AD7A77C2DC3A458008E616D /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1610;
				LastUpgradeCheck = 1610;
				TargetAttributes = {
					8AD7A7832DC3A458008E616D = {
						CreatedOnToolsVersion = 16.1;
					};
					8AD7A7932DC3A459008E616D = {
						CreatedOnToolsVersion = 16.1;
						TestTargetID = 8AD7A7832DC3A458008E616D;
					};
					8AD7A79D2DC3A459008E616D = {
						CreatedOnToolsVersion = 16.1;
						TestTargetID = 8AD7A7832DC3A458008E616D;
					};
				};
			};
			buildConfigurationList = 8AD7A77F2DC3A458008E616D /* Build configuration list for PBXProject "DoctorDuty" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"pt-BR",
			);
			mainGroup = 8AD7A77B2DC3A458008E616D;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 8AD7A7852DC3A458008E616D /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				8AD7A7832DC3A458008E616D /* DoctorDuty */,
				8AD7A7932DC3A459008E616D /* DoctorDutyTests */,
				8AD7A79D2DC3A459008E616D /* DoctorDutyUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		8AD7A7822DC3A458008E616D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8AD7A7C02DC3A772008E616D /* Localizable.xcstrings in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8AD7A7922DC3A459008E616D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8AD7A79C2DC3A459008E616D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		8AD7A7802DC3A458008E616D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8AD7A7902DC3A459008E616D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8AD7A79A2DC3A459008E616D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		8AD7A7962DC3A459008E616D /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 8AD7A7832DC3A458008E616D /* DoctorDuty */;
			targetProxy = 8AD7A7952DC3A459008E616D /* PBXContainerItemProxy */;
		};
		8AD7A7A02DC3A459008E616D /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 8AD7A7832DC3A458008E616D /* DoctorDuty */;
			targetProxy = 8AD7A79F2DC3A459008E616D /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		8AD7A7A62DC3A459008E616D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.1;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		8AD7A7A72DC3A459008E616D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.1;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_EMIT_LOC_STRINGS = YES;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		8AD7A7A92DC3A459008E616D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"DoctorDuty/Preview Content\"";
				DEVELOPMENT_TEAM = Z5UNWAXTR5;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSUserNotificationsUsageDescription = "Precisamos de sua permissão para enviar lembretes sobre seus plantões e notas fiscais.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.nvrApps.DoctorDuty;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		8AD7A7AA2DC3A459008E616D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"DoctorDuty/Preview Content\"";
				DEVELOPMENT_TEAM = Z5UNWAXTR5;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSUserNotificationsUsageDescription = "Precisamos de sua permissão para enviar lembretes sobre seus plantões e notas fiscais.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.nvrApps.DoctorDuty;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		8AD7A7AC2DC3A459008E616D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = Z5UNWAXTR5;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.1;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.nvrApps.DoctorDutyTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/DoctorDuty.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/DoctorDuty";
			};
			name = Debug;
		};
		8AD7A7AD2DC3A459008E616D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = Z5UNWAXTR5;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.1;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.nvrApps.DoctorDutyTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/DoctorDuty.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/DoctorDuty";
			};
			name = Release;
		};
		8AD7A7AF2DC3A459008E616D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = Z5UNWAXTR5;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.nvrApps.DoctorDutyUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = DoctorDuty;
			};
			name = Debug;
		};
		8AD7A7B02DC3A459008E616D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = Z5UNWAXTR5;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.nvrApps.DoctorDutyUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = DoctorDuty;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		8AD7A77F2DC3A458008E616D /* Build configuration list for PBXProject "DoctorDuty" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8AD7A7A62DC3A459008E616D /* Debug */,
				8AD7A7A72DC3A459008E616D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8AD7A7A82DC3A459008E616D /* Build configuration list for PBXNativeTarget "DoctorDuty" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8AD7A7A92DC3A459008E616D /* Debug */,
				8AD7A7AA2DC3A459008E616D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8AD7A7AB2DC3A459008E616D /* Build configuration list for PBXNativeTarget "DoctorDutyTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8AD7A7AC2DC3A459008E616D /* Debug */,
				8AD7A7AD2DC3A459008E616D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8AD7A7AE2DC3A459008E616D /* Build configuration list for PBXNativeTarget "DoctorDutyUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8AD7A7AF2DC3A459008E616D /* Debug */,
				8AD7A7B02DC3A459008E616D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 8AD7A77C2DC3A458008E616D /* Project object */;
}
