//
//  LocationStore.swift
//  DoctorDuty
//
//  Created by Augment Agent on 01/05/25.
//

import Foundation

class LocationStore: ObservableObject {
    @Published var locations: [Location] = []

    private static let locationsKey = "savedLocations"

    init() {
        loadLocations()
    }

    func addLocation(_ location: Location) {
        locations.append(location)
        saveLocations()
    }

    func updateLocation(_ location: Location) {
        if let index = locations.firstIndex(where: { $0.id == location.id }) {
            locations[index] = location
            saveLocations()
        }
    }

    func removeLocation(at indexSet: IndexSet) {
        locations.remove(atOffsets: indexSet)
        saveLocations()
    }

    func removeLocation(withID id: UUID) {
        if let index = locations.firstIndex(where: { $0.id == id }) {
            locations.remove(at: index)
            saveLocations()
        }
    }

    func toggleLocationActive(_ location: Location) {
        if let index = locations.firstIndex(where: { $0.id == location.id }) {
            var updatedLocation = location
            updatedLocation.isActive = !location.isActive
            locations[index] = updatedLocation
            saveLocations()
        }
    }

    private func saveLocations() {
        if let encoded = try? JSONEncoder().encode(locations) {
            UserDefaults.standard.set(encoded, forKey: Self.locationsKey)
        }
    }

    private func loadLocations() {
        if let data = UserDefaults.standard.data(forKey: Self.locationsKey),
           let decoded = try? JSONDecoder().decode([Location].self, from: data) {
            locations = decoded
        } else {
            // Carregar alguns locais de exemplo se não houver dados salvos
            locations = [
                Location(name: "Hospital São Lucas", colorHex: "#4CAF50"),  // Verde
                Location(name: "Hospital Santa Maria", colorHex: "#2196F3"), // Azul
                Location(name: "Clínica Saúde Total", colorHex: "#9C27B0"), // Roxo
                Location(name: "Hospital Regional", colorHex: "#FF9800")    // Laranja
            ]
            saveLocations()
        }
    }
}
