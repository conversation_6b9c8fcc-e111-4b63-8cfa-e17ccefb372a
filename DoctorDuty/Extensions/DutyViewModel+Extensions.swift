//
//  DutyViewModel+Extensions.swift
//  DoctorDuty
//
//  Created by Augment Agent on 01/05/25.
//

import Foundation
import SwiftUI

extension DutyViewModel {
    // MARK: - Formatters

    /// Returns a date formatter for displaying dates in full format
    var fullDateFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .full
        formatter.timeStyle = .none
        return formatter
    }

    /// Returns a date formatter for displaying dates in medium format
    var mediumDateFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        return formatter
    }

    /// Returns a date formatter for displaying month and year
    var monthYearFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateFormat = "MMMM yyyy"
        return formatter
    }

    /// Returns a currency formatter
    var currencyFormatter: NumberFormatter {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.locale = Locale.current
        return formatter
    }

    // MARK: - Formatting Methods

    /// Formats a date in full style
    func formatFullDate(_ date: Date) -> String {
        return fullDateFormatter.string(from: date)
    }

    /// Formats a date in medium style
    func formatMediumDate(_ date: Date) -> String {
        return mediumDateFormatter.string(from: date)
    }

    /// Formats a date as month and year
    func formatMonthYear(_ date: Date) -> String {
        return monthYearFormatter.string(from: date)
    }

    /// Formats an amount as currency
    func formatCurrency(_ amount: Double) -> String {
        return currencyFormatter.string(from: NSNumber(value: amount)) ?? "$0.00"
    }

    // MARK: - Validation Rules

    /// Validates if a duty can be saved
    func canSaveDuty(_ duty: Duty) -> Bool {
        return duty.isValid()
    }

    /// Validates if a duty can be set as recurring
    func canSetRecurring(_ duty: Duty) -> Bool {
        return duty.canBeRecurring()
    }

    // MARK: - Business Rules

    /// Creates a new duty with default values
    func createDefaultDuty(for date: Date) -> Duty {
        return Duty(
            date: date,
            location: "",
            paymentAmount: 0.0,
            isPaid: false,
            notes: nil,
            isLegalEntity: false,
            isNightShift: false,
            isRecurring: false,
            needsInvoiceReminder: false,
            invoiceReminderDays: nil
        )
    }

    /// Updates a duty with new values
    func updateDutyValues(duty: Duty,
                         location: String,
                         date: Date,
                         paymentAmount: Double,
                         isPaid: Bool,
                         notes: String?,
                         isLegalEntity: Bool,
                         isNightShift: Bool,
                         isRecurring: Bool,
                         recurrenceType: RecurrenceType?,
                         recurrenceEndDate: Date?,
                         needsInvoiceReminder: Bool = false,
                         invoiceReminderDays: Int? = nil) -> Duty {

        return Duty(
            id: duty.id,
            date: date,
            location: location,
            paymentAmount: paymentAmount,
            isPaid: isPaid,
            notes: notes?.isEmpty ?? true ? nil : notes,
            isLegalEntity: isLegalEntity,
            isNightShift: isNightShift,
            isRecurring: isRecurring,
            recurrenceType: isRecurring ? recurrenceType : nil,
            recurrenceEndDate: isRecurring ? recurrenceEndDate : nil,
            needsInvoiceReminder: needsInvoiceReminder,
            invoiceReminderDays: needsInvoiceReminder ? invoiceReminderDays : nil
        )
    }
}
