import SwiftUI

struct LocationSection: View {
    @ObservedObject var formViewModel: DutyFormViewModel
    var body: some View {
        Section {
            VStack(spacing: 12) {
                HStack {
                    Image(systemName: "mappin.and.ellipse")
                        .foregroundColor(.accentMedical)
                        .font(.system(size: 18))
                    Text("Local do Plantão")
                        .font(.headline)
                        .foregroundColor(.primary)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                Menu {
                    ForEach(formViewModel.availableLocations, id: \.self) { location in
                        Button(action: {
                            formViewModel.selectLocation(location)
                        }) {
                            HStack {
                                if location == formViewModel.addNewLocationOption {
                                    Image(systemName: "plus.circle.fill")
                                        .foregroundColor(.accentMedical)
                                    Text(location)
                                        .foregroundColor(.accentMedical)
                                        .fontWeight(.medium)
                                } else {
                                    Circle()
                                        .fill(formViewModel.colorForLocation(location))
                                        .frame(width: 12, height: 12)
                                        .padding(.trailing, 4)
                                    Text(location)
                                    if formViewModel.selectedLocation == location {
                                        Spacer()
                                        Image(systemName: "checkmark.circle.fill")
                                            .foregroundColor(.accentMedical)
                                    }
                                }
                            }
                        }
                    }
                } label: {
                    HStack {
                        if !formViewModel.selectedLocation.isEmpty && formViewModel.selectedLocation != formViewModel.addNewLocationOption {
                            Circle()
                                .fill(formViewModel.colorForLocation(formViewModel.selectedLocation))
                                .frame(width: 12, height: 12)
                                .padding(.trailing, 4)
                        }
                        Text(formViewModel.selectedLocation.isEmpty ? "Selecione um local" : formViewModel.selectedLocation)
                            .foregroundColor(formViewModel.selectedLocation.isEmpty ? .secondary : .primary)
                            .fontWeight(formViewModel.selectedLocation.isEmpty ? .regular : .medium)
                        Spacer()
                        Image(systemName: "chevron.down.circle.fill")
                            .foregroundColor(.accentMedical)
                            .font(.system(size: 18))
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 10)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .background(
                        RoundedRectangle(cornerRadius: 10)
                            .fill(Color.backgroundMedical.opacity(0.7))
                            .shadow(color: Color.black.opacity(0.05), radius: 2, x: 0, y: 1)
                    )
                }
                if formViewModel.selectedLocation.isEmpty {
                    HStack {
                        Image(systemName: "keyboard")
                            .foregroundColor(.secondary)
                            .font(.system(size: 14))
                        TextField("Digite o nome do local", text: $formViewModel.location)
                            .padding(.vertical, 8)
                    }
                    .padding(.horizontal, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color.secondary.opacity(0.3), lineWidth: 1)
                    )
                    .padding(.top, 4)
                }
                if let error = formViewModel.locationError {
                    HStack {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundColor(.red)
                            .font(.system(size: 12))
                        Text(error)
                            .font(.caption)
                            .foregroundColor(.red)
                    }
                    .padding(.top, 4)
                }
            }
            .padding(.vertical, 4)
        }
        .alert("Adicionar Novo Local", isPresented: $formViewModel.showingAddLocationAlert) {
            TextField("Nome do local", text: $formViewModel.newLocationName)
                .autocapitalization(.words)
            Button("Cancelar", role: .cancel) { }
            Button("Adicionar") {
                formViewModel.addNewLocation()
            }
        } message: {
            Text("Digite o nome do novo local para adicionar à lista de locais disponíveis.")
        }
    }
} 