//
//  DutyDetailView.swift
//  DoctorDuty
//
//  Created by Augment Agent on 01/05/25.
//

import SwiftUI

struct DutyDetailView: View {
    @EnvironmentObject var viewModel: DutyViewModel
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme

    let duty: Duty
    @State private var editedDuty: Duty
    @State private var isEditing = false
    @State private var showingDeleteConfirmation = false

    init(duty: Duty) {
        self.duty = duty
        self._editedDuty = State(initialValue: duty)
    }

    var body: some View {
        ScrollView {
            VStack(spacing: 16) {
                // Header Card with Location and Date
                headerCard

                // Details Card with Person Type and Shift Type
                detailsCard

                // Payment Card
                paymentCard

                // Recurrence Card (if applicable)
                if duty.isRecurring {
                    recurrenceCard
                }

                // Notes Card (if applicable)
                if let notes = duty.notes, !notes.isEmpty {
                    notesCard(notes: notes)
                }

                // Actions Card
                actionsCard

                Spacer(minLength: 20)
            }
            .padding(.horizontal, 16)
            .padding(.top, 8)
            .padding(.bottom, 16)
        }
        .background(Color.backgroundMedical.opacity(0.5).ignoresSafeArea())
        .navigationTitle("Detalhes do Plantão")
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                    isEditing = true
                }) {
                    Image(systemName: "pencil")
                        .foregroundColor(.accentMedical)
                }
            }
        }
        .sheet(isPresented: $isEditing) {
            DutyFormView(duty: duty)
                .environmentObject(viewModel)
                .onDisappear {
                    // Recarregar o duty após a edição
                    if let updatedDuty = viewModel.dutyStore.duties.first(where: { $0.id == duty.id }) {
                        editedDuty = updatedDuty
                    }
                }
        }
        .alert("Confirmar exclusão", isPresented: $showingDeleteConfirmation) {
            Button("Cancelar", role: .cancel) { }
            Button("Excluir", role: .destructive) {
                viewModel.deleteDuty(duty)
                dismiss()
            }
        } message: {
            Text("Tem certeza que deseja excluir este plantão? Esta ação não pode ser desfeita.")
        }
    }

    // MARK: - UI Components

    private var headerCard: some View {
        VStack(spacing: 0) {
            // Location header with color indicator
            HStack {
                Circle()
                    .fill(viewModel.colorForLocation(duty.location))
                    .frame(width: 16, height: 16)

                Text(duty.location)
                    .font(.title2)
                    .fontWeight(.bold)

                Spacer()

                // Status indicators
                HStack(spacing: 8) {
                    // Person type indicator
                    Text(duty.personTypeShortText)
                        .font(.caption)
                        .fontWeight(.bold)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(duty.personTypeBackgroundColor)
                        .cornerRadius(6)

                    // Shift type indicator
                    HStack(spacing: 4) {
                        Image(systemName: duty.isNightShift ? "moon.stars.fill" : "sun.max.fill")
                            .font(.system(size: 12))
                        Text(duty.shiftTypeText)
                            .font(.caption)
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(duty.shiftTypeBackgroundColor)
                    .cornerRadius(6)
                }
            }
            .padding(16)

            Divider()
                .padding(.horizontal, 16)

            // Date information
            HStack {
                Image(systemName: "calendar")
                    .foregroundColor(.accentMedical)

                Text(viewModel.formatFullDate(duty.date))
                    .font(.headline)

                Spacer()

                // Payment status badge
                Text(duty.paymentStatusText)
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(duty.paymentStatusColor)
                    .cornerRadius(6)
            }
            .padding(16)
        }
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.backgroundMedical)
                .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.gray.opacity(0.2), lineWidth: 1)
        )
    }

    private var detailsCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Section title
            Text("Detalhes")
                .font(.headline)
                .foregroundColor(.primary)
                .padding(.bottom, 4)

            // Person type
            HStack(spacing: 12) {
                Image(systemName: duty.isLegalEntity ? "building.2" : "person.fill")
                    .foregroundColor(.accentMedical)
                    .frame(width: 24)

                VStack(alignment: .leading, spacing: 4) {
                    Text("Tipo de Pessoa")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Text(duty.personTypeText)
                        .font(.subheadline)
                }

                Spacer()
            }
            .padding(.vertical, 4)

            // Shift type
            HStack(spacing: 12) {
                Image(systemName: duty.isNightShift ? "moon.stars.fill" : "sun.max.fill")
                    .foregroundColor(.accentMedical)
                    .frame(width: 24)

                VStack(alignment: .leading, spacing: 4) {
                    Text("Tipo de Plantão")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Text(duty.shiftTypeText)
                        .font(.subheadline)
                }

                Spacer()
            }
            .padding(.vertical, 4)
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.backgroundMedical)
                .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.gray.opacity(0.2), lineWidth: 1)
        )
    }

    private var paymentCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Section title
            Text("Pagamento")
                .font(.headline)
                .foregroundColor(.primary)
                .padding(.bottom, 4)

            // Payment amount
            HStack(alignment: .center, spacing: 12) {
                Image(systemName: "dollarsign.circle.fill")
                    .foregroundColor(.accentMedical)
                    .font(.system(size: 24))

                Text(viewModel.formatCurrency(duty.paymentAmount))
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(duty.isPaid ? .paidColor : .primary)

                Spacer()

                // Payment toggle
                VStack(alignment: .trailing, spacing: 6) {
                    Text(duty.isPaid ? "Pago" : "Pendente")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(duty.isPaid ? .paidColor : .unpaidColor)

                    Toggle("", isOn: Binding(
                        get: { duty.isPaid },
                        set: { newValue in
                            viewModel.togglePaymentStatus(for: duty)
                            // Update local state
                            if let updatedDuty = viewModel.dutyStore.duties.first(where: { $0.id == duty.id }) {
                                editedDuty = updatedDuty
                            }
                        }
                    ))
                    .labelsHidden()
                    .toggleStyle(SwitchToggleStyle(tint: .paidColor))
                    .scaleEffect(0.9)
                }
            }
            .padding(.vertical, 4)
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.backgroundMedical)
                .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.gray.opacity(0.2), lineWidth: 1)
        )
    }

    private var recurrenceCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Section title with icon
            HStack(spacing: 8) {
                Image(systemName: "repeat")
                    .foregroundColor(.accentMedical)

                Text("Recorrência")
                    .font(.headline)
                    .foregroundColor(.primary)
            }
            .padding(.bottom, 4)

            // Recurrence details
            VStack(alignment: .leading, spacing: 16) {
                if let recurrenceType = duty.recurrenceType {
                    HStack(spacing: 12) {
                        Image(systemName: "calendar.badge.clock")
                            .foregroundColor(.accentMedical)
                            .frame(width: 24)

                        VStack(alignment: .leading, spacing: 4) {
                            Text("Frequência")
                                .font(.caption)
                                .foregroundColor(.secondary)

                            Text(recurrenceType.rawValue)
                                .font(.subheadline)
                        }

                        Spacer()
                    }
                    .padding(.vertical, 4)
                }

                if let endDate = duty.recurrenceEndDate {
                    HStack(spacing: 12) {
                        Image(systemName: "calendar.badge.exclamationmark")
                            .foregroundColor(.accentMedical)
                            .frame(width: 24)

                        VStack(alignment: .leading, spacing: 4) {
                            Text("Data Final")
                                .font(.caption)
                                .foregroundColor(.secondary)

                            Text(viewModel.formatFullDate(endDate))
                                .font(.subheadline)
                        }

                        Spacer()
                    }
                    .padding(.vertical, 4)
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.backgroundMedical)
                .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.gray.opacity(0.2), lineWidth: 1)
        )
    }

    private func notesCard(notes: String) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            // Section title with icon
            HStack(spacing: 8) {
                Image(systemName: "text.quote")
                    .foregroundColor(.accentMedical)

                Text("Observações")
                    .font(.headline)
                    .foregroundColor(.primary)
            }
            .padding(.bottom, 4)

            // Notes content
            Text(notes)
                .font(.body)
                .fixedSize(horizontal: false, vertical: true)
                .padding(.horizontal, 4)
                .padding(.vertical, 4)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.backgroundMedical)
                .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.gray.opacity(0.2), lineWidth: 1)
        )
    }

    private var actionsCard: some View {
        HStack(spacing: 16) {
            // Edit button
            Button(action: {
                isEditing = true
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "pencil")
                    Text("Editar")
                        .fontWeight(.medium)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 14)
                .background(Color.accentMedical.opacity(0.2))
                .foregroundColor(.accentMedical)
                .cornerRadius(10)
            }

            // Delete button
            Button(action: {
                showingDeleteConfirmation = true
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "trash")
                    Text("Excluir")
                        .fontWeight(.medium)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 14)
                .background(Color.red.opacity(0.1))
                .foregroundColor(.red)
                .cornerRadius(10)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.backgroundMedical)
                .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.gray.opacity(0.2), lineWidth: 1)
        )
    }
}

#Preview {
    let viewModel = DutyViewModel()
    viewModel.locationStore = LocationStore()

    return NavigationStack {
        DutyDetailView(duty: Duty.sampleDuties[0])
            .environmentObject(viewModel)
    }
}

struct DutyDetailView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // Preview with a paid duty
            PaidDutyPreview()
                .previewDisplayName("Paid Duty with Recurrence")

            // Preview with an unpaid duty
            UnpaidDutyPreview()
                .previewDisplayName("Unpaid Duty")
                .preferredColorScheme(.dark)
        }
    }
}

// Helper preview views
struct PaidDutyPreview: View {
    var body: some View {
        let viewModel = DutyViewModel()
        viewModel.locationStore = LocationStore()

        return NavigationStack {
            DutyDetailView(duty: Duty(
                date: Date(),
                location: "Hospital São Lucas",
                paymentAmount: 1250.00,
                isPaid: true,
                notes: "Plantão de emergência com observações adicionais para testar o layout de múltiplas linhas no card de observações.",
                isLegalEntity: true,
                isNightShift: true,
                isRecurring: true,
                recurrenceType: .weekly,
                recurrenceEndDate: Calendar.current.date(byAdding: .month, value: 3, to: Date())
            ))
            .environmentObject(viewModel)
        }
    }
}

struct UnpaidDutyPreview: View {
    var body: some View {
        let viewModel = DutyViewModel()
        viewModel.locationStore = LocationStore()

        return NavigationStack {
            DutyDetailView(duty: Duty(
                date: Date(),
                location: "Clínica Médica",
                paymentAmount: 850.00,
                isPaid: false,
                notes: nil,
                isLegalEntity: false,
                isNightShift: false
            ))
            .environmentObject(viewModel)
        }
    }
}
