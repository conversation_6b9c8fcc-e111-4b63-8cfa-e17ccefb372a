//
//  CurrencyTextField.swift
//  DoctorDuty
//
//  Created by Augment Agent on 01/05/25.
//

import SwiftUI

struct CurrencyTextField: UIViewRepresentable {
    @Binding var value: Double
    var placeholder: String = "R$ 0,00"

    func makeUIView(context: Context) -> UITextField {
        let textField = UITextField()
        textField.keyboardType = .decimalPad
        textField.textAlignment = .right
        textField.delegate = context.coordinator
        textField.textAlignment = .right
        textField.placeholder = placeholder

        // Adiciona toolbar com botão Done
        let toolbar = UIToolbar()
        toolbar.sizeToFit()
        let doneButton = UIBarButtonItem(
            title: "Pronto",
            style: .done,
            target: context.coordinator,
            action: #selector(Coordinator.doneButtonTapped)
        )
        let flexSpace = UIBarButtonItem(
            barButtonSystemItem: .flexibleSpace,
            target: nil,
            action: nil
        )
        toolbar.items = [flexSpace, doneButton]
        textField.inputAccessoryView = toolbar

        return textField
    }

    func updateUIView(_ uiView: UITextField, context: Context) {
        // Atualiza o texto apenas se o valor for diferente para evitar loop
        let formatter = NumberFormatter()
        formatter.numberStyle = .decimal
        formatter.minimumFractionDigits = 2
        formatter.maximumFractionDigits = 2
        formatter.decimalSeparator = ","

        let formattedValue = formatter.string(from: NSNumber(value: value)) ?? "0,00"
        if uiView.text != formattedValue {
            uiView.text = formattedValue
        }
        uiView.backgroundColor = .clear
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(value: $value)
    }

    class Coordinator: NSObject, UITextFieldDelegate {
        @Binding var value: Double

        init(value: Binding<Double>) {
            _value = value
        }

        @objc func doneButtonTapped() {
            UIApplication.shared.sendAction(
                #selector(UIResponder.resignFirstResponder),
                to: nil,
                from: nil,
                for: nil
            )
        }

        func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
            let currentText = textField.text ?? ""
            let newString = (currentText as NSString).replacingCharacters(in: range, with: string)
            let digits = newString.compactMap { $0.wholeNumberValue }.map(String.init).joined()

            guard digits.count <= 11 else { return false }

            let doubleValue = (Double(digits) ?? 0) / 100
            let formatter = NumberFormatter()
            formatter.numberStyle = .decimal
            formatter.minimumFractionDigits = 2
            formatter.maximumFractionDigits = 2
            formatter.decimalSeparator = ","

            textField.text = formatter.string(from: NSNumber(value: doubleValue)) ?? "0,00"
            value = doubleValue
            return false
        }
    }
}

struct CurrencyTextField_Previews: PreviewProvider {
    static var previews: some View {
        CurrencyTextField(value: .constant(123.45))
            .padding()
            .previewLayout(.sizeThatFits)
    }
}
