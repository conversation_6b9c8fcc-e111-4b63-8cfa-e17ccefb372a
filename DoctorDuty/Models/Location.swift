//
//  Location.swift
//  DoctorDuty
//
//  Created by Augment Agent on 01/05/25.
//

import Foundation
import SwiftUI

struct Location: Identifiable, Codable, Hashable {
    var id: UUID
    var name: String
    var address: String?
    var isActive: Bool
    var colorHex: String

    init(id: UUID = UUID(), name: String, address: String? = nil, isActive: Bool = true, colorHex: String = "#4CAF50") {
        self.id = id
        self.name = name
        self.address = address
        self.isActive = isActive
        self.colorHex = colorHex
    }

    // Converte o código hexadecimal para Color
    var color: Color {
        Color(hex: colorHex) ?? .green
    }
}

// Extensão para converter código hexadecimal para Color
extension Color {
    init?(hex: String) {
        var hexSanitized = hex.trimmingCharacters(in: .whitespacesAndNewlines)
        hexSanitized = hexSanitized.replacingOccurrences(of: "#", with: "")

        var rgb: UInt64 = 0

        guard Scanner(string: hexSanitized).scanHexInt64(&rgb) else { return nil }

        let red = Double((rgb & 0xFF0000) >> 16) / 255.0
        let green = Double((rgb & 0x00FF00) >> 8) / 255.0
        let blue = Double(rgb & 0x0000FF) / 255.0

        self.init(red: red, green: green, blue: blue)
    }

    // Converte Color para código hexadecimal
    func toHex() -> String {
        guard let components = UIColor(self).cgColor.components else { return "#000000" }

        let r = Float(components[0])
        let g = Float(components[1])
        let b = Float(components[2])

        return String(format: "#%02lX%02lX%02lX", lroundf(r * 255), lroundf(g * 255), lroundf(b * 255))
    }
}
