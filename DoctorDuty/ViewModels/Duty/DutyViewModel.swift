//
//  DutyViewModel.swift
//  DoctorDuty
//
//  Created by <PERSON><PERSON><PERSON> on 01/05/25.
//

import Foundation
import SwiftUI

class DutyViewModel: ObservableObject {
    @Published var dutyStore: DutyStore
    @Published var savedLocations: [String] = []
    @Published var isLoading: Bool = false
    @Published var loadingError: String? = nil
    @Published var loadingState: LoadingState = .idle

    var locationStore: LocationStore?
    private let locationPersistence: LocationPersistence

    // Estados de carregamento
    enum LoadingState {
        case idle
        case loadingCache
        case loadingServer
        case loaded
        case error
    }

    init(dutyStore: DutyStore = DutyStore(), locationStore: LocationStore? = nil, locationPersistence: LocationPersistence = LocationPersistenceService()) {
        self.dutyStore = dutyStore
        self.locationStore = locationStore
        self.locationPersistence = locationPersistence
        loadSavedLocations()
    }

    // MARK: - Data Operations

    /// Carrega os plantões em duas etapas: primeiro do cache local e depois do servidor
    func loadDuties() async {
        await loadFromCache()
        await loadFromServer()
    }

    /// Carrega os plantões do cache local
    private func loadFromCache() async {
        DispatchQueue.main.async {
            self.isLoading = true
            self.loadingState = .loadingCache
            self.loadingError = nil
        }

        do {
            try await dutyStore.loadFromCache()

            DispatchQueue.main.async {
                // Após carregar os plantões do cache, extrair locais únicos
                self.updateLocationsFromDuties()

                // Se não houver dados no cache, manteremos o estado de carregamento
                // para que o indicador continue visível enquanto carregamos do servidor
                if !self.dutyStore.duties.isEmpty {
                    self.isLoading = false
                    self.loadingState = .loaded
                }
            }
        } catch {
            print("Error loading duties from cache: \(error)")
            // Não atualizamos o estado de erro aqui, pois ainda tentaremos carregar do servidor
        }
    }

    /// Carrega os plantões do servidor
    private func loadFromServer() async {
        DispatchQueue.main.async {
            self.loadingState = .loadingServer
            self.isLoading = true
            self.loadingError = nil
        }

        do {
            // Simula um atraso de rede para demonstrar o carregamento
            try await Task.sleep(nanoseconds: 1_500_000_000) // 1.5 segundos

            try await dutyStore.loadFromServer()

            DispatchQueue.main.async {
                // Após carregar os plantões do servidor, extrair locais únicos
                self.updateLocationsFromDuties()
                self.isLoading = false
                self.loadingState = .loaded
            }
        } catch {
            DispatchQueue.main.async {
                print("Error loading duties from server: \(error)")
                self.loadingError = "Não foi possível carregar os plantões do servidor. \(error.localizedDescription)"
                self.isLoading = false
                self.loadingState = .error
            }
        }
    }

    func saveDuties() async {
        do {
            try await dutyStore.save()
        } catch {
            print("Error saving duties: \(error)")
        }
    }

    // MARK: - Locations Management

    /// Carrega os locais salvos do UserDefaults
    private func loadSavedLocations() {
        self.savedLocations = locationPersistence.loadSavedLocations()
    }

    /// Salva os locais no UserDefaults
    private func saveSavedLocations() {
        locationPersistence.saveSavedLocations(savedLocations)
    }

    /// Adiciona um novo local à lista de locais salvos
    func addLocation(_ location: String) {
        guard !location.isBlank else { return }

        let trimmedLocation = location.trimmingCharacters(in: .whitespacesAndNewlines)

        // Verificar se o local já existe (ignorando maiúsculas/minúsculas)
        if !savedLocations.contains(where: { $0.lowercased() == trimmedLocation.lowercased() }) {
            savedLocations.append(trimmedLocation)
            saveSavedLocations()
        }

        // Adicionar ao LocationStore se disponível
        if let locationStore = locationStore {
            let newLocation = Location(name: trimmedLocation)
            locationStore.addLocation(newLocation)
        }
    }

    /// Remove um local da lista de locais salvos
    func removeLocation(_ location: String) {
        if let index = savedLocations.firstIndex(of: location) {
            savedLocations.remove(at: index)
            saveSavedLocations()
        }
    }

    /// Atualiza a lista de locais com base nos plantões existentes
    func updateLocationsFromDuties() {
        let uniqueLocations = Set(dutyStore.duties.map { $0.location })

        for location in uniqueLocations {
            if !location.isEmpty && !savedLocations.contains(location) {
                savedLocations.append(location)

                // Adicionar ao LocationStore se disponível
                if let locationStore = locationStore {
                    let newLocation = Location(name: location)
                    locationStore.addLocation(newLocation)
                }
            }
        }

        if !savedLocations.isEmpty {
            saveSavedLocations()
        }
    }

    /// Retorna os locais ativos do LocationStore
    func getActiveLocations() -> [String] {
        if let locationStore = locationStore {
            return locationStore.locations
                .filter { $0.isActive }
                .map { $0.name }
        }
        return savedLocations
    }

    /// Retorna a cor associada a um local
    func colorForLocation(_ locationName: String) -> Color {
        if let locationStore = locationStore,
           let location = locationStore.locations.first(where: { $0.name == locationName }) {
            return location.color
        }
        return .accentMedical
    }

    // MARK: - Duty Management

    func addDuty(_ duty: Duty) {
        // Validate duty before adding
        guard duty.isValid() else {
            print("Cannot add invalid duty")
            return
        }

        dutyStore.add(duty)
        Task {
            await saveDuties()
        }
    }

    func updateDuty(_ duty: Duty) {
        // Validate duty before updating
        guard duty.isValid() else {
            print("Cannot update with invalid duty data")
            return
        }

        dutyStore.update(duty)
        Task {
            await saveDuties()
        }
    }

    func deleteDuty(_ duty: Duty) {
        dutyStore.delete(duty)
        Task {
            await saveDuties()
        }
    }

    func deleteDuties(at offsets: IndexSet) {
        let sortedDuties = dutyStore.duties.sorted(by: { $0.date < $1.date })
        for index in offsets {
            dutyStore.delete(sortedDuties[index])
        }
        Task {
            await saveDuties()
        }
    }

    /// Creates and adds a new duty with the provided values
    func createAndAddDuty(date: Date,
                         location: String,
                         paymentAmount: Double,
                         isPaid: Bool = false,
                         notes: String? = nil,
                         isLegalEntity: Bool = false,
                         isNightShift: Bool = false,
                         isRecurring: Bool = false,
                         recurrenceType: RecurrenceType? = nil,
                         recurrenceEndDate: Date? = nil,
                         needsInvoiceReminder: Bool = false,
                         invoiceReminderDays: Int? = nil) {

        let newDuty = Duty(
            date: date,
            location: location,
            paymentAmount: paymentAmount,
            isPaid: isPaid,
            notes: notes,
            isLegalEntity: isLegalEntity,
            isNightShift: isNightShift,
            isRecurring: isRecurring,
            recurrenceType: recurrenceType,
            recurrenceEndDate: recurrenceEndDate,
            needsInvoiceReminder: needsInvoiceReminder,
            invoiceReminderDays: invoiceReminderDays
        )

        addDuty(newDuty)

        // If recurring, create additional duties
        if isRecurring && recurrenceType != nil && recurrenceEndDate != nil {
            createRecurringDuties(basedOn: newDuty)
        }
    }

    /// Toggles the payment status of a duty
    func togglePaymentStatus(for duty: Duty) {
        var updatedDuty = duty
        updatedDuty.isPaid.toggle()
        updateDuty(updatedDuty)
    }

    // MARK: - Duty Queries

    func dutiesForDate(_ date: Date) -> [Duty] {
        return dutyStore.dutiesForDate(date)
    }

    func sortedDuties() -> [Duty] {
        return dutyStore.duties.sorted(by: { $0.date < $1.date })
    }

    /// Returns all duties that are paid
    func paidDuties() -> [Duty] {
        return dutyStore.duties.filter { $0.isPaid }
    }

    /// Returns all duties that are unpaid
    func unpaidDuties() -> [Duty] {
        return dutyStore.duties.filter { !$0.isPaid }
    }

    /// Returns all duties for a specific month
    func dutiesForMonth(_ date: Date) -> [Duty] {
        let calendar = Calendar.current
        return dutyStore.duties.filter { duty in
            let dutyComponents = calendar.dateComponents([.year, .month], from: duty.date)
            let targetComponents = calendar.dateComponents([.year, .month], from: date)
            return dutyComponents.year == targetComponents.year && dutyComponents.month == targetComponents.month
        }
    }

    /// Returns all duties for a specific location
    func dutiesForLocation(_ location: String) -> [Duty] {
        return dutyStore.duties.filter { $0.location == location }
    }

    /// Returns all duties of a specific type (legal entity or individual)
    func dutiesForPersonType(isLegalEntity: Bool) -> [Duty] {
        return dutyStore.duties.filter { $0.isLegalEntity == isLegalEntity }
    }

    /// Returns all duties of a specific shift type (night or day)
    func dutiesForShiftType(isNightShift: Bool) -> [Duty] {
        return dutyStore.duties.filter { $0.isNightShift == isNightShift }
    }

    /// Returns the total payment amount for all duties
    func totalPaymentAmount() -> Double {
        return dutyStore.duties.reduce(0) { $0 + $1.paymentAmount }
    }

    /// Returns the total paid amount
    func totalPaidAmount() -> Double {
        return paidDuties().reduce(0) { $0 + $1.paymentAmount }
    }

    /// Returns the total unpaid amount
    func totalUnpaidAmount() -> Double {
        return unpaidDuties().reduce(0) { $0 + $1.paymentAmount }
    }

    // MARK: - Recurring Duties

    func createRecurringDuties(basedOn duty: Duty) {
        guard duty.isRecurring, let recurrenceType = duty.recurrenceType, let endDate = duty.recurrenceEndDate else {
            return
        }

        let calendar = Calendar.current
        var currentDate = duty.date

        // Determine interval based on recurrence type
        var dateComponents = DateComponents()
        switch recurrenceType {
        case .daily:
            dateComponents.day = 1
        case .weekly:
            dateComponents.day = 7
        case .biweekly:
            dateComponents.day = 14
        case .monthly:
            dateComponents.month = 1
        }

        // Create duties until end date
        while let nextDate = calendar.date(byAdding: dateComponents, to: currentDate), nextDate <= endDate {
            let recurringDuty = Duty(
                date: nextDate,
                location: duty.location,
                paymentAmount: duty.paymentAmount,
                isPaid: false,
                notes: duty.notes,
                isLegalEntity: duty.isLegalEntity,
                isNightShift: duty.isNightShift,
                isRecurring: false,
                needsInvoiceReminder: duty.needsInvoiceReminder,
                invoiceReminderDays: duty.invoiceReminderDays
            )

            dutyStore.add(recurringDuty)
            currentDate = nextDate
        }

        Task {
            await saveDuties()
        }
    }

    // MARK: - Calendar Helpers

    /// Returns the days of the week as short symbols
    func daysOfWeek() -> [String] {
        let formatter = DateFormatter()
        formatter.locale = Locale.current
        return formatter.shortWeekdaySymbols
    }

    /// Returns an array of dates representing the days in the month for the given date
    func daysInMonth(for date: Date) -> [Date?] {
        let calendar = Calendar.current
        let range = calendar.range(of: .day, in: .month, for: date)!
        let firstDayOfMonth = calendar.date(from: calendar.dateComponents([.year, .month], from: date))!

        let firstWeekday = calendar.component(.weekday, from: firstDayOfMonth)
        let weekdayOffset = calendar.firstWeekday - 1
        let leadingEmptyCells = (firstWeekday - weekdayOffset + 7) % 7

        var days = Array(repeating: nil as Date?, count: leadingEmptyCells)

        for day in 1...range.count {
            if let date = calendar.date(byAdding: .day, value: day - 1, to: firstDayOfMonth) {
                days.append(date)
            }
        }

        // Add trailing empty cells to make the count a multiple of 7
        let trailingEmptyCells = (7 - (days.count % 7)) % 7
        days.append(contentsOf: Array(repeating: nil as Date?, count: trailingEmptyCells))

        return days
    }

    /// Checks if a date has any duties scheduled
    func hasDuties(on date: Date) -> Bool {
        return !dutiesForDate(date).isEmpty
    }

    /// Checks if a date is today
    func isToday(_ date: Date) -> Bool {
        return Calendar.current.isDateInToday(date)
    }

    /// Checks if two dates are in the same day
    func isSameDay(_ date1: Date, _ date2: Date) -> Bool {
        return Calendar.current.isDate(date1, inSameDayAs: date2)
    }
}