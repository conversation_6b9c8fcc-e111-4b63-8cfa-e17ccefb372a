//
//  StatisticsView.swift
//  DoctorDuty
//
//  Created by Augment Agent on 01/05/25.
//

import SwiftUI
import Charts

struct StatisticsView: View {
    @EnvironmentObject var dutyViewModel: DutyViewModel
    @StateObject private var viewModel: StatisticsViewModel
    @State private var showingFilters = false
    @State private var selectedView = 0 // 0 = Mensal, 1 = Anual
    @Environment(\.colorScheme) private var colorScheme

    init() {
        // Initialize with a temporary viewModel that will be replaced by the @EnvironmentObject
        let tempViewModel = DutyViewModel()
        self._viewModel = StateObject(wrappedValue: StatisticsViewModel(dutyViewModel: tempViewModel))
    }

    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: 16) {
                    // View Selector
                    Picker("Visualização", selection: $selectedView) {
                        Text("Mensal").tag(0)
                        Text("Anual").tag(1)
                    }
                    .pickerStyle(.segmented)
                    .padding(.horizontal)
                    .padding(.top, 8)
                    .padding(.bottom, 4)

                    // Summary Cards
                    if selectedView == 0 {
                        VStack(spacing: 20) {
                            // Monthly Summary
                            if let monthData = viewModel.selectedMonthData() {
                                SummaryCard(
                                    title: "\(monthData.monthName) \(viewModel.selectedYear)",
                                    totalAmount: monthData.totalAmount,
                                    paidAmount: monthData.paidAmount,
                                    dutyCount: monthData.dutyCount,
                                    viewModel: viewModel
                                )
                                .padding(.horizontal, 16)
                            }

                            // Monthly Charts
                            monthlyChartSection
                        }
                    } else {
                        VStack(spacing: 20) {
                            // Annual Summary
                            if let yearData = viewModel.currentYearData() {
                                SummaryCard(
                                    title: "Ano \(yearData.year)",
                                    totalAmount: yearData.totalAmount,
                                    paidAmount: yearData.paidAmount,
                                    dutyCount: yearData.dutyCount,
                                    viewModel: viewModel
                                )
                                .padding(.horizontal, 16)
                            }

                            // Annual Charts
                            annualChartSection
                        }
                    }
                }
            }
            .background(Color.backgroundMedical.opacity(0.5).ignoresSafeArea())
            .navigationTitle("Estatísticas")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        showingFilters = true
                    }) {
                        Image(systemName: "line.3.horizontal.decrease.circle")
                            .foregroundColor(.accentMedical)
                    }
                }

                ToolbarItem(placement: .principal) {
                    Text("Estatísticas")
                        .font(.headline)
                        .foregroundColor(.primary)
                }
            }
            .sheet(isPresented: $showingFilters) {
                StatisticsFilterView(viewModel: viewModel)
            }
            .onAppear {
                // Update the viewModel with the correct dutyViewModel from the environment
                if viewModel.dutyViewModel !== dutyViewModel {
                    viewModel.updateDutyViewModel(dutyViewModel)
                }
            }
        }
    }

    // MARK: - UI Components

    private var monthlyChartSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Plantões Mensais")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)

                Spacer()

                Picker("Ano", selection: $viewModel.selectedYear) {
                    ForEach(viewModel.availableYears(), id: \.self) { year in
                        Text("\(year)").tag(year)
                    }
                }
                .pickerStyle(.menu)
            }
            .padding(.horizontal, 16)

            // Monthly Chart
            ChartContainer {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Faturamento Mensal")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                        .padding(.leading, 4)

                    Chart {
                        ForEach(viewModel.currentAndPastMonthlyData()) { stat in
                            BarMark(
                                x: .value("Mês", stat.monthName),
                                y: .value("Valor", stat.totalAmount)
                            )
                            .foregroundStyle(Color.accentMedical.gradient)
                            .annotation(position: .top, alignment: .trailing) {
                                if stat.month == Calendar.current.component(.month, from: Date()) &&
                                    viewModel.selectedYear == Calendar.current.component(.year, from: Date()) {
                                    Text(viewModel.formatCurrencyCompact(stat.totalAmount))
                                        .font(.system(size: 9))
                                        .fontWeight(.bold)
                                        .foregroundColor(Color.accentMedical)
                                        .padding(.vertical, 2)
                                        .padding(.horizontal, 4)
                                        .background(
                                            RoundedRectangle(cornerRadius: 4)
                                                .fill(Color.backgroundMedical.opacity(0.8))
                                                .shadow(color: Color.black.opacity(0.1), radius: 1, x: 0, y: 1)
                                        )
                                        .offset(y: -5)
                                }
                            }
                        }
                    }
                    .chartXAxis {
                        AxisMarks(values: .automatic) { value in
                            AxisValueLabel {
                                if let month = value.as(String.self) {
                                    Text(month.prefix(3))
                                        .font(.caption)
                                        .foregroundColor(colorScheme == .dark ? Color.white.opacity(0.8) : Color.primary)
                                }
                            }

                            // Adicionar linhas de grade verticais
                            AxisGridLine(stroke: StrokeStyle(lineWidth: 0.5, dash: [2, 2]))
                                .foregroundStyle(Color.gray.opacity(0.3))
                        }
                    }
                    .chartYAxis {
                        AxisMarks(position: .leading) { value in
                            AxisValueLabel {
                                if let value = value.as(Double.self) {
                                    Text(viewModel.formatCurrencyCompact(value))
                                        .font(.caption)
                                        .foregroundColor(colorScheme == .dark ? Color.white.opacity(0.8) : Color.primary)
                                }
                            }

                            // Adicionar linhas de grade horizontais
                            AxisGridLine(stroke: StrokeStyle(lineWidth: 0.5, dash: [2, 2]))
                                .foregroundStyle(Color.gray.opacity(0.3))
                        }
                    }
                    .frame(height: 220)
                }
            }
            .padding(.horizontal, 16)

            // Monthly Paid vs Unpaid Chart
            ChartContainer {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Pagamentos Mensais")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                        .padding(.leading, 4)

                    HStack(spacing: 16) {
                        HStack(spacing: 6) {
                            RoundedRectangle(cornerRadius: 2)
                                .fill(Color.paidColor)
                                .frame(width: 10, height: 10)
                            Text("Pago")
                                .font(.caption)
                                .foregroundColor(.primary)
                        }

                        HStack(spacing: 6) {
                            RoundedRectangle(cornerRadius: 2)
                                .fill(Color.unpaidColor)
                                .frame(width: 10, height: 10)
                            Text("A Receber")
                                .font(.caption)
                                .foregroundColor(.primary)
                        }
                    }
                    .padding(.leading, 4)
                    .padding(.bottom, 4)

                    Chart {
                        ForEach(viewModel.currentAndPastMonthlyData()) { stat in
                            BarMark(
                                x: .value("Mês", stat.monthName),
                                y: .value("Pago", stat.paidAmount)
                            )
                            .foregroundStyle(Color.paidColor.gradient)

                            BarMark(
                                x: .value("Mês", stat.monthName),
                                y: .value("A Receber", stat.unpaidAmount)
                            )
                            .foregroundStyle(Color.unpaidColor.gradient)
                        }
                    }
                    .chartXAxis {
                        AxisMarks(values: .automatic) { value in
                            AxisValueLabel {
                                if let month = value.as(String.self) {
                                    Text(month.prefix(3))
                                        .font(.caption)
                                        .foregroundColor(colorScheme == .dark ? Color.white.opacity(0.8) : Color.primary)
                                }
                            }

                            // Adicionar linhas de grade verticais
                            AxisGridLine(stroke: StrokeStyle(lineWidth: 0.5, dash: [2, 2]))
                                .foregroundStyle(Color.gray.opacity(0.3))
                        }
                    }
                    .chartYAxis {
                        AxisMarks(position: .leading) { value in
                            AxisValueLabel {
                                if let value = value.as(Double.self) {
                                    Text(viewModel.formatCurrencyCompact(value))
                                        .font(.caption)
                                        .foregroundColor(colorScheme == .dark ? Color.white.opacity(0.8) : Color.primary)
                                }
                            }

                            // Adicionar linhas de grade horizontais
                            AxisGridLine(stroke: StrokeStyle(lineWidth: 0.5, dash: [2, 2]))
                                .foregroundStyle(Color.gray.opacity(0.3))
                        }
                    }
                    .frame(height: 220)
                }
            }
            .padding(.horizontal, 16)
        }
    }

    private var annualChartSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Plantões Anuais")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)

                Spacer()

                Picker("Ano", selection: $viewModel.selectedYear) {
                    ForEach(viewModel.availableYears(), id: \.self) { year in
                        Text("\(year)").tag(year)
                    }
                }
                .pickerStyle(.menu)
            }
            .padding(.horizontal, 16)

            // Annual Chart
            ChartContainer {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Faturamento Anual")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                        .padding(.leading, 4)

                    Chart {
                        ForEach(viewModel.annualData()) { stat in
                            BarMark(
                                x: .value("Ano", String(stat.year)),
                                y: .value("Valor", stat.totalAmount)
                            )
                            .foregroundStyle(Color.accentMedical.gradient)
                            .annotation(position: .top) {
                                if stat.year == viewModel.selectedYear {
                                    Text(viewModel.formatCurrencyCompact(stat.totalAmount))
                                        .font(.system(size: 9))
                                        .fontWeight(.bold)
                                        .foregroundColor(Color.accentMedical)
                                        .padding(.vertical, 2)
                                        .padding(.horizontal, 4)
                                        .background(
                                            RoundedRectangle(cornerRadius: 4)
                                                .fill(Color.backgroundMedical.opacity(0.8))
                                                .shadow(color: Color.black.opacity(0.1), radius: 1, x: 0, y: 1)
                                        )
                                        .offset(y: -5)
                                }
                            }
                        }
                    }
                    .chartYAxis {
                        AxisMarks(position: .leading) { value in
                            AxisValueLabel {
                                if let value = value.as(Double.self) {
                                    Text(viewModel.formatCurrencyCompact(value))
                                        .font(.caption)
                                        .foregroundColor(colorScheme == .dark ? Color.white.opacity(0.8) : Color.primary)
                                }
                            }

                            // Adicionar linhas de grade horizontais
                            AxisGridLine(stroke: StrokeStyle(lineWidth: 0.5, dash: [2, 2]))
                                .foregroundStyle(Color.gray.opacity(0.3))
                        }
                    }
                    .chartXAxis {
                        AxisMarks { value in
                            AxisValueLabel()

                            // Adicionar linhas de grade verticais
                            AxisGridLine(stroke: StrokeStyle(lineWidth: 0.5, dash: [2, 2]))
                                .foregroundStyle(Color.gray.opacity(0.3))
                        }
                    }
                    .frame(height: 220)
                }
            }
            .padding(.horizontal, 16)

            // Annual Paid vs Unpaid Chart
            ChartContainer {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Pagamentos Anuais")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                        .padding(.leading, 4)

                    // Legenda com cores consistentes
                    HStack(spacing: 16) {
                        HStack(spacing: 6) {
                            RoundedRectangle(cornerRadius: 2)
                                .fill(Color.paidColor)
                                .frame(width: 10, height: 10)
                            Text("Pago")
                                .font(.caption)
                                .foregroundColor(.primary)
                        }

                        HStack(spacing: 6) {
                            RoundedRectangle(cornerRadius: 2)
                                .fill(Color.unpaidColor)
                                .frame(width: 10, height: 10)
                            Text("A Receber")
                                .font(.caption)
                                .foregroundColor(.primary)
                        }
                    }
                    .padding(.leading, 4)
                    .padding(.bottom, 4)

                    // Gráfico de barras empilhadas
                    Chart {
                        ForEach(viewModel.annualData()) { stat in
                            BarMark(
                                x: .value("Ano", String(stat.year)),
                                y: .value("Pago", stat.paidAmount)
                            )
                            .foregroundStyle(Color.paidColor.gradient)
                            .annotation(position: .overlay) {
                                if stat.year == viewModel.selectedYear && stat.paidAmount > 0 {
                                    Text(viewModel.formatCurrencyCompact(stat.paidAmount))
                                        .font(.system(size: 9))
                                        .fontWeight(.bold)
                                        .foregroundColor(.white)
                                        .padding(.vertical, 2)
                                        .padding(.horizontal, 4)
                                        .background(
                                            RoundedRectangle(cornerRadius: 4)
                                                .fill(Color.paidColor.opacity(0.9))
                                        )
                                }
                            }

                            BarMark(
                                x: .value("Ano", String(stat.year)),
                                y: .value("A Receber", stat.unpaidAmount)
                            )
                            .foregroundStyle(Color.unpaidColor.gradient)
                            .annotation(position: .overlay) {
                                if stat.year == viewModel.selectedYear && stat.unpaidAmount > 0 {
                                    Text(viewModel.formatCurrencyCompact(stat.unpaidAmount))
                                        .font(.system(size: 9))
                                        .fontWeight(.bold)
                                        .foregroundColor(.white)
                                        .padding(.vertical, 2)
                                        .padding(.horizontal, 4)
                                        .background(
                                            RoundedRectangle(cornerRadius: 4)
                                                .fill(Color.unpaidColor.opacity(0.9))
                                        )
                                }
                            }
                        }
                    }
                    .chartYAxis {
                        AxisMarks(position: .leading) { value in
                            AxisValueLabel {
                                if let value = value.as(Double.self) {
                                    Text(viewModel.formatCurrencyCompact(value))
                                        .font(.caption)
                                        .foregroundColor(colorScheme == .dark ? Color.white.opacity(0.8) : Color.primary)
                                }
                            }

                            // Adicionar linhas de grade horizontais
                            AxisGridLine(stroke: StrokeStyle(lineWidth: 0.5, dash: [2, 2]))
                                .foregroundStyle(Color.gray.opacity(0.3))
                        }
                    }
                    .chartXAxis {
                        AxisMarks { value in
                            AxisValueLabel()

                            // Adicionar linhas de grade verticais
                            AxisGridLine(stroke: StrokeStyle(lineWidth: 0.5, dash: [2, 2]))
                                .foregroundStyle(Color.gray.opacity(0.3))
                        }
                    }
                    .frame(height: 220)
                }
            }
            .padding(.horizontal, 16)
        }
    }
}

// MARK: - Supporting Views

struct SummaryCard: View {
    let title: String
    let totalAmount: Double
    let paidAmount: Double
    let dutyCount: Int
    let viewModel: StatisticsViewModel
    @Environment(\.colorScheme) private var colorScheme

    var unpaidAmount: Double {
        return totalAmount - paidAmount
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(title)
                .font(.headline)
                .foregroundColor(.primary)
                .padding(.bottom, 4)

            HStack(spacing: 2) {
                StatItem(
                    title: "Total",
                    value: viewModel.formatCurrency(totalAmount),
                    icon: "dollarsign.circle.fill",
                    color: .accentMedical
                )

                Divider().padding(.horizontal, 2)

                StatItem(
                    title: "Pago",
                    value: viewModel.formatCurrency(paidAmount),
                    icon: "checkmark.circle.fill",
                    color: .paidColor
                )

                Divider().padding(.horizontal, 2)

                StatItem(
                    title: "A Receber",
                    value: viewModel.formatCurrency(unpaidAmount),
                    icon: "clock.fill",
                    color: .unpaidColor
                )

                Divider().padding(.horizontal, 2)

                StatItem(
                    title: "Plantões",
                    value: "\(dutyCount)",
                    icon: "calendar.badge.clock",
                    color: .blue
                )
            }
        }
        .padding()
        .background(Color.backgroundMedical)
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.gray.opacity(0.2), lineWidth: 1)
        )
        .shadow(color: Color.black.opacity(colorScheme == .dark ? 0.2 : 0.05),
                radius: colorScheme == .dark ? 3 : 5,
                x: 0,
                y: 2)
    }
}

struct StatItem: View {
    let title: String
    let value: String
    let icon: String
    let color: Color

    var body: some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .foregroundColor(color)
                .font(.system(size: 20))

            Text(value)
                .font(.caption)
                .fontWeight(.bold)
                .foregroundColor(.primary)
                .lineLimit(1)
                .minimumScaleFactor(0.7)

            Text(title)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
}

struct ChartContainer<Content: View>: View {
    let content: Content
    @Environment(\.colorScheme) private var colorScheme

    init(@ViewBuilder content: () -> Content) {
        self.content = content()
    }

    var body: some View {
        VStack {
            content
        }
        .padding(.vertical, 16)
        .padding(.horizontal, 12)
        .background(Color.backgroundMedical)
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.gray.opacity(0.2), lineWidth: 1)
        )
        .shadow(color: Color.black.opacity(colorScheme == .dark ? 0.2 : 0.05),
                radius: colorScheme == .dark ? 3 : 5,
                x: 0,
                y: 2)
    }
}

struct StatisticsFilterView: View {
    @ObservedObject var viewModel: StatisticsViewModel
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        NavigationStack {
            Form {
                Section(header: Text("Filtrar por Local")) {
                    Toggle("Filtrar por Local", isOn: $viewModel.filterByLocation)
                        .toggleStyle(SwitchToggleStyle(tint: .accentMedical))

                    if viewModel.filterByLocation {
                        ForEach(viewModel.availableLocations, id: \.self) { location in
                            Button(action: {
                                viewModel.toggleLocation(location)
                            }) {
                                HStack {
                                    Text(location)
                                    Spacer()
                                    if viewModel.selectedLocations.contains(location) {
                                        Image(systemName: "checkmark")
                                            .foregroundColor(.accentMedical)
                                    }
                                }
                            }
                            .foregroundColor(.primary)
                        }

                        Button("Limpar Filtros") {
                            viewModel.clearLocationFilters()
                        }
                        .foregroundColor(.unpaidColor)
                    }
                }
            }
            .navigationTitle("Filtros")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Pronto") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    let dutyViewModel = DutyViewModel()
    return StatisticsView()
        .environmentObject(dutyViewModel)
}
