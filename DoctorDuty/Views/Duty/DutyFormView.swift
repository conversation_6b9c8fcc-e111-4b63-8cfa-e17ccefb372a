//
//  DutyFormView.swift
//  DoctorDuty
//
//  Created by Augment Agent on 01/05/25.
//

import SwiftUI
import Combine
import UIKit

struct DutyFormView: View {
    @EnvironmentObject var viewModel: DutyViewModel
    @Environment(\.dismiss) private var dismiss
    @StateObject private var formViewModel: DutyFormViewModel
    @State private var showingNotificationPreview: Bool = false
    
    // Centraliza a lógica de inicialização do formViewModel
    private static func makeFormViewModel(date: Date? = nil, duty: Duty? = nil) -> DutyFormViewModel {
        let tempViewModel = DutyViewModel()
        if let duty = duty {
            return DutyFormViewModel(dutyViewModel: tempViewModel, duty: duty)
        } else if let date = date {
            return DutyFormViewModel(dutyViewModel: tempViewModel, date: date)
        } else {
            return DutyFormViewModel(dutyViewModel: tempViewModel, date: Date())
        }
    }
    
    init(date: Date) {
        _formViewModel = StateObject(wrappedValue: Self.makeFormViewModel(date: date))
    }
    
    init(duty: Duty, isNewDuty: Bool = false) {
        _formViewModel = StateObject(wrappedValue: Self.makeFormViewModel(duty: duty))
    }
    
    init(duty: Binding<Duty>, isNewDuty: Bool = false) {
        _formViewModel = StateObject(wrappedValue: Self.makeFormViewModel(duty: duty.wrappedValue))
    }
    
    var body: some View {
        NavigationStack {
            ZStack {
                // Gradient background
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.accentMedical.opacity(0.05),
                        Color.white
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: 20) {
                        // Header with icon and title
                        headerSection
                        
                        // Main form content
                        VStack(spacing: 16) {
                            locationCard
                            basicInfoCard
                            paymentCard
                            advancedOptionsCard
                            notesCard
                        }
                        .padding(.horizontal, 16)
                        .padding(.bottom, 20)
                    }
                }
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarLeading) {
                        Button("Cancelar") {
                            dismiss()
                        }
                        .foregroundColor(.secondary)
                    }
                    
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("Salvar") {
                            if formViewModel.saveDuty() {
                                dismiss()
                            }
                        }
                        .fontWeight(.semibold)
                        .foregroundColor(formViewModel.isFormValid ? .accentMedical : .gray)
                        .disabled(!formViewModel.isFormValid)
                    }
                }
            }
        }
        .onAppear {
            formViewModel.dutyViewModel = viewModel
        }
        .animation(.spring(response: 0.5, dampingFraction: 0.8), value: formViewModel.showAdvancedOptions)
        .animation(.spring(response: 0.5, dampingFraction: 0.8), value: formViewModel.isRecurring)
        .animation(.spring(response: 0.5, dampingFraction: 0.8), value: formViewModel.needsInvoiceReminder)
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 8) {
            ZStack {
                Circle()
                    .fill(Color.accentMedical.opacity(0.1))
                    .frame(width: 60, height: 60)
                
                Image(systemName: "stethoscope")
                    .font(.system(size: 28, weight: .medium))
                    .foregroundColor(.accentMedical)
            }
            
            Text("Novo Plantão")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            Text("Preencha as informações do plantão médico")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(.top, 20)
        .padding(.horizontal, 16)
    }
    
    // MARK: - Location Card
    private var locationCard: some View {
        CardContainer(icon: "mappin.and.ellipse", title: "Local do Plantão", iconColor: .blue) {
            VStack(spacing: 12) {
                Menu {
                    ForEach(formViewModel.availableLocations, id: \.self) { location in
                        Button(action: {
                            formViewModel.selectLocation(location)
                        }) {
                            HStack {
                                Circle()
                                    .fill(formViewModel.colorForLocation(location))
                                    .frame(width: 12, height: 12)
                                Text(location)
                            }
                        }
                    }
                    
                    Divider()
                    
                    Button(action: {
                        formViewModel.selectLocation(formViewModel.addNewLocationOption)
                    }) {
                        Label("Adicionar novo local", systemImage: "plus.circle")
                    }
                } label: {
                    HStack {
                        if !formViewModel.selectedLocation.isEmpty {
                            Circle()
                                .fill(formViewModel.colorForLocation(formViewModel.selectedLocation))
                                .frame(width: 16, height: 16)
                        }
                        
                        Text(formViewModel.selectedLocation.isEmpty ? "Selecione um local" : formViewModel.selectedLocation)
                            .foregroundColor(formViewModel.selectedLocation.isEmpty ? .secondary : .primary)
                        
                        Spacer()
                        
                        Image(systemName: "chevron.down")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 10)
                            .fill(Color.gray.opacity(0.1))
                    )
                }
                
                if let error = formViewModel.locationError {
                    HStack {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundColor(.red)
                            .font(.caption)
                        Text(error)
                            .font(.caption)
                            .foregroundColor(.red)
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                }
            }
        }
        .alert("Adicionar Novo Local", isPresented: $formViewModel.showingAddLocationAlert) {
            TextField("Nome do local", text: $formViewModel.newLocationName)
                .autocapitalization(.words)
            Button("Cancelar", role: .cancel) { }
            Button("Adicionar") {
                formViewModel.addNewLocation()
            }
        } message: {
            Text("Digite o nome do novo local para adicionar à lista de locais disponíveis.")
        }
    }
    
    // MARK: - Basic Info Card
    private var basicInfoCard: some View {
        CardContainer(icon: "calendar.badge.clock", title: "Informações Básicas", iconColor: .green) {
            VStack(spacing: 16) {
                // Date Selection
                VStack(alignment: .leading, spacing: 8) {
                    Label("Data do Plantão", systemImage: "calendar")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.secondary)
                    
                    MiniCalendarView(selectedDate: $formViewModel.date)
                }
                
                // Person Type and Shift Type
                HStack(spacing: 12) {
                    VStack(alignment: .leading, spacing: 8) {
                        Label("Tipo de Pessoa", systemImage: "person.fill")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.secondary)
                        
                        Picker("", selection: $formViewModel.isLegalEntity) {
                            Label("PF", systemImage: "person.fill").tag(false)
                            Label("PJ", systemImage: "building.2.fill").tag(true)
                        }
                        .pickerStyle(.segmented)
                    }
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Label("Turno", systemImage: "clock")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.secondary)
                        
                        Picker("", selection: $formViewModel.isNightShift) {
                            Label("Dia", systemImage: "sun.max.fill").tag(false)
                            Label("Noite", systemImage: "moon.fill").tag(true)
                        }
                        .pickerStyle(.segmented)
                    }
                }
            }
        }
    }
    
    // MARK: - Payment Card
    private var paymentCard: some View {
        CardContainer(icon: "dollarsign.circle.fill", title: "Pagamento", iconColor: .orange) {
            VStack(spacing: 16) {
                VStack(alignment: .leading, spacing: 8) {
                    Label("Valor do Plantão", systemImage: "banknote")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.secondary)
                    
                    RightAlignedDecimalField(
                        value: $formViewModel.paymentAmount,
                        placeholder: "R$ 0,00"
                    )
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 10)
                            .fill(Color.gray.opacity(0.1))
                    )
                }
                
                Toggle(isOn: $formViewModel.isPaid) {
                    Label("Plantão já foi pago", systemImage: "checkmark.circle.fill")
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                .toggleStyle(SwitchToggleStyle(tint: .accentMedical))
            }
        }
    }
    
    // MARK: - Advanced Options Card
    private var advancedOptionsCard: some View {
        CardContainer(icon: "gearshape.fill", title: "Opções Avançadas", iconColor: .purple) {
            VStack(spacing: 16) {
                Button(action: {
                    withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                        formViewModel.showAdvancedOptions.toggle()
                    }
                }) {
                    HStack {
                        Text("Mostrar opções avançadas")
                            .font(.subheadline)
                            .fontWeight(.medium)
                        
                        Spacer()
                        
                        Image(systemName: formViewModel.showAdvancedOptions ? "chevron.up" : "chevron.down")
                            .font(.caption)
                            .foregroundColor(.accentMedical)
                    }
                    .foregroundColor(.primary)
                }
                
                if formViewModel.showAdvancedOptions {
                    VStack(spacing: 16) {
                        // Recurring toggle
                        Toggle(isOn: $formViewModel.isRecurring) {
                            Label("Plantão Recorrente", systemImage: "arrow.triangle.2.circlepath")
                                .font(.subheadline)
                                .fontWeight(.medium)
                        }
                        .toggleStyle(SwitchToggleStyle(tint: .accentMedical))
                        
                        if formViewModel.isRecurring {
                            VStack(spacing: 12) {
                                VStack(alignment: .leading, spacing: 8) {
                                    Label("Frequência", systemImage: "calendar.badge.clock")
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                        .foregroundColor(.secondary)
                                    
                                    Picker("Frequência", selection: $formViewModel.recurrenceType) {
                                        ForEach(RecurrenceType.allCases, id: \.self) { type in
                                            Text(type.rawValue).tag(type)
                                        }
                                    }
                                    .pickerStyle(.menu)
                                    .accentColor(.accentMedical)
                                }
                                
                                VStack(alignment: .leading, spacing: 8) {
                                    Label("Data Final", systemImage: "calendar.badge.exclamationmark")
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                        .foregroundColor(.secondary)
                                    
                                    DatePicker("", selection: $formViewModel.recurrenceEndDate, displayedComponents: .date)
                                        .datePickerStyle(.compact)
                                        .accentColor(.accentMedical)
                                }
                            }
                        }
                        
                        // Invoice reminder (only for PJ)
                        if formViewModel.isLegalEntity {
                            Toggle(isOn: $formViewModel.needsInvoiceReminder) {
                                Label("Lembrete de Nota Fiscal", systemImage: "bell.fill")
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                            }
                            .toggleStyle(SwitchToggleStyle(tint: .accentMedical))
                            
                            if formViewModel.needsInvoiceReminder {
                                VStack(alignment: .leading, spacing: 8) {
                                    Label("Dias para lembrete", systemImage: "clock.badge.exclamationmark")
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                        .foregroundColor(.secondary)
                                    
                                    Stepper(value: $formViewModel.invoiceReminderDays, in: 1...30) {
                                        Text("\(formViewModel.invoiceReminderDays) dias após o plantão")
                                            .font(.subheadline)
                                    }
                                }
                            }
                        }
                    }
                    .transition(.opacity.combined(with: .move(edge: .top)))
                }
            }
        }
    }
    
    // MARK: - Notes Card
    private var notesCard: some View {
        CardContainer(icon: "note.text", title: "Observações", iconColor: .indigo) {
            VStack(alignment: .leading, spacing: 8) {
                Label("Informações adicionais", systemImage: "text.alignleft")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
                
                ZStack(alignment: .topLeading) {
                    TextEditor(text: $formViewModel.notes)
                        .frame(minHeight: 100)
                        .padding(12)
                        .background(
                            RoundedRectangle(cornerRadius: 10)
                                .fill(Color.gray.opacity(0.1))
                        )
                    
                    if formViewModel.notes.isEmpty {
                        Text("Adicione informações adicionais sobre o plantão...")
                            .foregroundColor(.secondary)
                            .padding(.horizontal, 16)
                            .padding(.top, 20)
                            .allowsHitTesting(false)
                    }
                }
            }
        }
    }
}

// MARK: - Card Container Component
struct CardContainer<Content: View>: View {
    let icon: String
    let title: String
    let iconColor: Color
    let content: Content

    init(icon: String, title: String, iconColor: Color, @ViewBuilder content: () -> Content) {
        self.icon = icon
        self.title = title
        self.iconColor = iconColor
        self.content = content()
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Card Header
            HStack(spacing: 12) {
                ZStack {
                    Circle()
                        .fill(iconColor.opacity(0.1))
                        .frame(width: 40, height: 40)

                    Image(systemName: icon)
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(iconColor)
                }

                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Spacer()
            }

            // Card Content
            content
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white)
                .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }
}

#Preview {
    let viewModel = DutyViewModel()
    viewModel.locationStore = LocationStore()
    return DutyFormView(date: Date())
        .environmentObject(viewModel)
}
