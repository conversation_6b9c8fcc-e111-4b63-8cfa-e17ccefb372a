//
//  Date+Extensions.swift
//  DoctorDuty
//
//  Created by Augment Agent on 01/05/25.
//

import Foundation

extension Date {
    /// Returns the start of the day (midnight)
    var startOfDay: Date {
        return Calendar.current.startOfDay(for: self)
    }
    
    /// Returns the end of the day (23:59:59)
    var endOfDay: Date {
        var components = DateComponents()
        components.day = 1
        components.second = -1
        return Calendar.current.date(byAdding: components, to: startOfDay)!
    }
    
    /// Returns the start of the month
    var startOfMonth: Date {
        let calendar = Calendar.current
        let components = calendar.dateComponents([.year, .month], from: self)
        return calendar.date(from: components)!
    }
    
    /// Returns the end of the month
    var endOfMonth: Date {
        let calendar = Calendar.current
        var components = DateComponents()
        components.month = 1
        components.day = -1
        return calendar.date(byAdding: components, to: startOfMonth)!
    }
    
    /// Returns a date with the specified number of days added
    func addingDays(_ days: Int) -> Date {
        return Calendar.current.date(byAdding: .day, value: days, to: self)!
    }
    
    /// Returns a date with the specified number of months added
    func addingMonths(_ months: Int) -> Date {
        return Calendar.current.date(byAdding: .month, value: months, to: self)!
    }
    
    /// Returns true if the date is in the past
    var isPast: Bool {
        return self < Date()
    }
    
    /// Returns true if the date is in the future
    var isFuture: Bool {
        return self > Date()
    }
    
    /// Returns true if the date is today
    var isToday: Bool {
        return Calendar.current.isDateInToday(self)
    }
    
    /// Returns the day component of the date
    var day: Int {
        return Calendar.current.component(.day, from: self)
    }
    
    /// Returns the month component of the date
    var month: Int {
        return Calendar.current.component(.month, from: self)
    }
    
    /// Returns the year component of the date
    var year: Int {
        return Calendar.current.component(.year, from: self)
    }
    
    /// Returns true if the date is in the same day as another date
    func isSameDay(as date: Date) -> Bool {
        return Calendar.current.isDate(self, inSameDayAs: date)
    }
    
    /// Returns true if the date is in the same month as another date
    func isSameMonth(as date: Date) -> Bool {
        let calendar = Calendar.current
        let selfComponents = calendar.dateComponents([.year, .month], from: self)
        let dateComponents = calendar.dateComponents([.year, .month], from: date)
        return selfComponents.year == dateComponents.year && selfComponents.month == dateComponents.month
    }
}
