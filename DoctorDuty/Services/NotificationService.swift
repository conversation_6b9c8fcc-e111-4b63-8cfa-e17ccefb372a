//
//  NotificationService.swift
//  DoctorDuty
//
//  Created by Augment Agent on 01/05/25.
//

import Foundation
import UserNotifications

class NotificationService {
    static let shared = NotificationService()
    
    private init() {}
    
    // Solicita permissão para enviar notificações
    func requestPermission(completion: @escaping (Bool) -> Void) {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { granted, error in
            DispatchQueue.main.async {
                if let error = error {
                    print("Erro ao solicitar permissão para notificações: \(error.localizedDescription)")
                    completion(false)
                    return
                }
                
                completion(granted)
            }
        }
    }
    
    // Verifica o status atual da permissão
    func checkPermissionStatus(completion: @escaping (UNAuthorizationStatus) -> Void) {
        UNUserNotificationCenter.current().getNotificationSettings { settings in
            DispatchQueue.main.async {
                completion(settings.authorizationStatus)
            }
        }
    }
    
    // Agenda uma notificação para lembrete de plantão
    func scheduleDutyReminder(for duty: Duty, hoursBeforeDuty: Int) {
        let content = UNMutableNotificationContent()
        content.title = "Lembrete de Plantão"
        content.body = "Você tem um plantão em \(duty.location) em \(hoursBeforeDuty) horas."
        content.sound = .default
        
        // Calcular o tempo para a notificação
        let triggerDate = Calendar.current.date(byAdding: .hour, value: -hoursBeforeDuty, to: duty.date)!
        let components = Calendar.current.dateComponents([.year, .month, .day, .hour, .minute], from: triggerDate)
        let trigger = UNCalendarNotificationTrigger(dateMatching: components, repeats: false)
        
        // Criar o pedido de notificação
        let identifier = "duty-reminder-\(duty.id.uuidString)"
        let request = UNNotificationRequest(identifier: identifier, content: content, trigger: trigger)
        
        // Adicionar o pedido ao centro de notificações
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("Erro ao agendar notificação: \(error.localizedDescription)")
            }
        }
    }
    
    // Agenda uma notificação para lembrete de nota fiscal
    func scheduleInvoiceReminder(for duty: Duty) {
        guard duty.needsInvoiceReminder, let reminderDays = duty.invoiceReminderDays else { return }
        
        let content = UNMutableNotificationContent()
        content.title = "Lembrete de Nota Fiscal"
        content.body = "Emita a nota fiscal do plantão em \(duty.location) (prazo: \(reminderDays) dias)."
        content.sound = .default
        
        // Calcular o tempo para a notificação
        let triggerDate = Calendar.current.date(byAdding: .day, value: reminderDays, to: duty.date)!
        let components = Calendar.current.dateComponents([.year, .month, .day, .hour, .minute], from: triggerDate)
        let trigger = UNCalendarNotificationTrigger(dateMatching: components, repeats: false)
        
        // Criar o pedido de notificação
        let identifier = "invoice-reminder-\(duty.id.uuidString)"
        let request = UNNotificationRequest(identifier: identifier, content: content, trigger: trigger)
        
        // Adicionar o pedido ao centro de notificações
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("Erro ao agendar notificação de nota fiscal: \(error.localizedDescription)")
            }
        }
    }
    
    // Cancela todas as notificações para um plantão específico
    func cancelNotifications(for dutyId: UUID) {
        let identifiers = [
            "duty-reminder-\(dutyId.uuidString)",
            "invoice-reminder-\(dutyId.uuidString)"
        ]
        UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: identifiers)
    }
    
    // Cancela todas as notificações pendentes
    func cancelAllNotifications() {
        UNUserNotificationCenter.current().removeAllPendingNotificationRequests()
    }
}
