//
//  ErrorView.swift
//  DoctorDuty
//
//  Created by Augment Agent on 01/05/25.
//

import SwiftUI

struct ErrorView: View {
    let message: String
    let retryAction: () -> Void

    var body: some View {
        VStack(spacing: 12) {
            HStack(alignment: .top, spacing: 12) {
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.title2)
                    .foregroundColor(.orange)

                VStack(alignment: .leading, spacing: 4) {
                    Text("Erro ao carregar dados")
                        .font(.headline)
                        .foregroundColor(.primary)

                    Text(message)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .fixedSize(horizontal: false, vertical: true)
                }

                Spacer()
            }

            Button(action: retryAction) {
                HStack {
                    Image(systemName: "arrow.clockwise")
                    Text("Tentar novamente")
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(Color.accentMedical)
                .foregroundColor(.white)
                .cornerRadius(8)
            }
            .buttonStyle(ScaleButtonStyle())
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.8))
                .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
        )
        .padding(.horizontal)
    }
}

// Nota: ScaleButtonStyle está definido em CalendarView.swift

#Preview {
    ErrorView(message: "Não foi possível conectar ao servidor. Verifique sua conexão com a internet e tente novamente.") {
        print("Retry tapped")
    }
    .padding()
    .background(Color.gray.opacity(0.1))
}
