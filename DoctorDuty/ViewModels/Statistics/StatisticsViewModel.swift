//
//  StatisticsViewModel.swift
//  DoctorDuty
//
//  Created by Augment Agent on 01/05/25.
//

import SwiftUI
import Combine

class StatisticsViewModel: ObservableObject {
    // Reference to the main ViewModel
    private(set) var dutyViewModel: DutyViewModel

    // Published properties for UI updates
    @Published var selectedYear: Int
    @Published var selectedMonth: Int
    @Published var showingMonthlyChart = true
    @Published var showingAnnualChart = true
    @Published var filterByLocation = false
    @Published var selectedLocations: Set<String> = []

    // Calendar reference
    private let calendar = Calendar.current

    // Initializer
    init(dutyViewModel: DutyViewModel) {
        self.dutyViewModel = dutyViewModel

        // Initialize with current year and month
        let currentDate = Date()
        self.selectedYear = calendar.component(.year, from: currentDate)
        self.selectedMonth = calendar.component(.month, from: currentDate)
    }

    // MARK: - Data Calculations

    /// Returns monthly data for the selected year
    func monthlyData() -> [MonthlyStatistic] {
        var statistics: [MonthlyStatistic] = []

        for month in 1...12 {
            let monthName = monthName(for: month)
            let duties = dutiesForYearAndMonth(year: selectedYear, month: month)
            let totalAmount = duties.reduce(0) { $0 + $1.paymentAmount }
            let paidAmount = duties.filter { $0.isPaid }.reduce(0) { $0 + $1.paymentAmount }
            let dutyCount = duties.count

            statistics.append(MonthlyStatistic(
                month: month,
                monthName: monthName,
                totalAmount: totalAmount,
                paidAmount: paidAmount,
                dutyCount: dutyCount
            ))
        }

        return statistics
    }

    /// Returns monthly data for the selected year, showing only current and past months
    func currentAndPastMonthlyData() -> [MonthlyStatistic] {
        let currentYear = Calendar.current.component(.year, from: Date())
        let currentMonth = Calendar.current.component(.month, from: Date())

        let allMonthlyData = monthlyData()

        // Se o ano selecionado for futuro, não mostra nenhum mês
        if selectedYear > currentYear {
            return []
        }

        // Se o ano selecionado for o ano atual, mostra apenas os meses até o mês atual
        if selectedYear == currentYear {
            return allMonthlyData.filter { $0.month <= currentMonth }
        }

        // Se o ano selecionado for passado, mostra todos os meses
        return allMonthlyData
    }

    /// Returns annual data for multiple years
    func annualData() -> [AnnualStatistic] {
        // Get all years from duties
        let years = availableYears()
        var statistics: [AnnualStatistic] = []

        for year in years {
            let duties = dutiesForYear(year: year)
            let totalAmount = duties.reduce(0) { $0 + $1.paymentAmount }
            let paidAmount = duties.filter { $0.isPaid }.reduce(0) { $0 + $1.paymentAmount }
            let dutyCount = duties.count

            statistics.append(AnnualStatistic(
                year: year,
                totalAmount: totalAmount,
                paidAmount: paidAmount,
                dutyCount: dutyCount
            ))
        }

        return statistics.sorted(by: { $0.year < $1.year })
    }

    /// Returns data for the selected month
    func selectedMonthData() -> MonthlyStatistic? {
        return monthlyData().first(where: { $0.month == selectedMonth })
    }

    /// Returns data for the current year
    func currentYearData() -> AnnualStatistic? {
        return annualData().first(where: { $0.year == selectedYear })
    }

    /// Returns duties for a specific year and month
    func dutiesForYearAndMonth(year: Int, month: Int) -> [Duty] {
        var dateComponents = DateComponents()
        dateComponents.year = year
        dateComponents.month = month
        dateComponents.day = 1

        guard let date = calendar.date(from: dateComponents) else {
            return []
        }

        let duties = dutyViewModel.dutiesForMonth(date)

        // Apply location filter if needed
        if filterByLocation && !selectedLocations.isEmpty {
            return duties.filter { selectedLocations.contains($0.location) }
        }

        return duties
    }

    /// Returns duties for a specific year
    func dutiesForYear(year: Int) -> [Duty] {
        var dateComponents = DateComponents()
        dateComponents.year = year
        dateComponents.month = 1
        dateComponents.day = 1

        guard let startDate = calendar.date(from: dateComponents) else {
            return []
        }

        dateComponents.year = year
        dateComponents.month = 12
        dateComponents.day = 31

        guard let endDate = calendar.date(from: dateComponents) else {
            return []
        }

        let duties = dutyViewModel.sortedDuties().filter { duty in
            duty.date >= startDate && duty.date <= endDate
        }

        // Apply location filter if needed
        if filterByLocation && !selectedLocations.isEmpty {
            return duties.filter { selectedLocations.contains($0.location) }
        }

        return duties
    }

    /// Returns all available years from duties
    func availableYears() -> [Int] {
        let years = dutyViewModel.sortedDuties().map { calendar.component(.year, from: $0.date) }
        return Array(Set(years)).sorted()
    }

    /// Returns all available locations
    var availableLocations: [String] {
        let locations = dutyViewModel.getActiveLocations()
        return locations.sorted()
    }

    // MARK: - Helper Methods

    /// Returns the name of a month
    func monthName(for month: Int) -> String {
        var dateComponents = DateComponents()
        dateComponents.month = month

        guard let date = calendar.date(from: dateComponents) else {
            return ""
        }

        let formatter = DateFormatter()
        formatter.dateFormat = "MMMM"
        return formatter.string(from: date)
    }

    /// Formats a currency value
    func formatCurrency(_ value: Double) -> String {
        return dutyViewModel.formatCurrency(value)
    }

    /// Formats a currency value in a compact form for chart axes
    func formatCurrencyCompact(_ value: Double) -> String {
        if value == 0 {
            return "0"
        }

        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.locale = Locale.current
        formatter.maximumFractionDigits = 0

        // Remover o símbolo de moeda para economizar espaço
        let currencySymbol = formatter.currencySymbol ?? "R$"

        if value >= 1_000_000 {
            let millions = value / 1_000_000
            formatter.maximumFractionDigits = millions < 10 ? 1 : 0
            var formattedValue = formatter.string(from: NSNumber(value: millions)) ?? "0"
            formattedValue = formattedValue.replacingOccurrences(of: currencySymbol, with: "")
            return formattedValue + "M"
        } else if value >= 1_000 {
            let thousands = value / 1_000
            formatter.maximumFractionDigits = thousands < 10 ? 1 : 0
            var formattedValue = formatter.string(from: NSNumber(value: thousands)) ?? "0"
            formattedValue = formattedValue.replacingOccurrences(of: currencySymbol, with: "")
            return formattedValue + "K"
        } else {
            var formattedValue = formatter.string(from: NSNumber(value: value)) ?? "0"
            formattedValue = formattedValue.replacingOccurrences(of: currencySymbol, with: "")
            return formattedValue
        }
    }

    /// Toggle selection of a location for filtering
    func toggleLocation(_ location: String) {
        if selectedLocations.contains(location) {
            selectedLocations.remove(location)
        } else {
            selectedLocations.insert(location)
        }
    }

    /// Clear all location filters
    func clearLocationFilters() {
        selectedLocations.removeAll()
    }

    /// Updates the reference to the DutyViewModel
    func updateDutyViewModel(_ viewModel: DutyViewModel) {
        self.dutyViewModel = viewModel
        objectWillChange.send()
    }
}

// MARK: - Data Models

struct MonthlyStatistic: Identifiable {
    var id: Int { month }
    let month: Int
    let monthName: String
    let totalAmount: Double
    let paidAmount: Double
    let dutyCount: Int

    var unpaidAmount: Double {
        return totalAmount - paidAmount
    }
}

struct AnnualStatistic: Identifiable {
    var id: Int { year }
    let year: Int
    let totalAmount: Double
    let paidAmount: Double
    let dutyCount: Int

    var unpaidAmount: Double {
        return totalAmount - paidAmount
    }
}
