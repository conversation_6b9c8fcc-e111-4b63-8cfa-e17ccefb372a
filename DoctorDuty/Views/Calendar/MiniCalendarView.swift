//
//  MiniCalendarView.swift
//  DoctorDuty
//
//  Created by Augment Agent on 01/05/25.
//

import SwiftUI

struct MiniCalendarView: View {
    @Binding var selectedDate: Date
    @State private var showCalendar = false

    private let calendar = Calendar.current

    var body: some View {
        VStack(alignment: .leading) {
            // Date display with button to show calendar
            But<PERSON>(action: {
                showCalendar.toggle()
            }) {
                HStack {
                    Text(dateFormatter.string(from: selectedDate))
                        .foregroundColor(.primary)
                        .font(.subheadline)

                    Spacer()

                    Image(systemName: "calendar")
                        .foregroundColor(.accentMedical)
                        .font(.system(size: 16))
                }
                .padding(.vertical, 8)
                .padding(.horizontal, 12)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.backgroundMedical.opacity(0.7))
                )
            }

            // Popup calendar
            if showCalendar {
                VStack {
                    // Month navigation
                    HStack {
                        Button(action: {
                            if let newDate = calendar.date(byAdding: .month, value: -1, to: selectedDate) {
                                selectedDate = newDate
                            }
                        }) {
                            Image(systemName: "chevron.left")
                                .foregroundColor(.accentMedical)
                        }

                        Spacer()

                        Text(monthYearFormatter.string(from: selectedDate))
                            .font(.subheadline)
                            .fontWeight(.medium)

                        Spacer()

                        Button(action: {
                            if let newDate = calendar.date(byAdding: .month, value: 1, to: selectedDate) {
                                selectedDate = newDate
                            }
                        }) {
                            Image(systemName: "chevron.right")
                                .foregroundColor(.accentMedical)
                        }
                    }
                    .padding(.horizontal, 8)
                    .padding(.top, 8)

                    // Calendar
                    DatePicker("", selection: $selectedDate, displayedComponents: .date)
                        .datePickerStyle(.graphical)
                        .labelsHidden()
                        .onChange(of: selectedDate) { _, _ in
                            // Close calendar after selection
                            showCalendar = false
                        }
                        .padding(8)
                }
                .background(Color.backgroundMedical)
                .cornerRadius(10)
                .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
                .padding(.top, 4)
                .zIndex(1) // Ensure calendar appears above other elements
            }
        }
    }

    private var dateFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        return formatter
    }

    private var monthYearFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateFormat = "MMMM yyyy"
        return formatter
    }
}

#Preview {
    MiniCalendarView(selectedDate: .constant(Date()))
        .frame(width: 300)
}
