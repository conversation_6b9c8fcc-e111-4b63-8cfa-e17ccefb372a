//
//  LocationViewModel.swift
//  DoctorDuty
//
//  Created by Augment Agent on 01/05/25.
//

import SwiftUI
import Combine

class LocationViewModel: ObservableObject {
    // Referência ao LocationStore
    @Published private(set) var locationStore: LocationStore

    // Propriedades publicadas para atualizações na UI
    @Published var searchText: String = ""
    @Published var isAddingLocation: Bool = false
    @Published var isEditingLocation: Bool = false
    @Published var selectedLocation: Location? = nil

    // Propriedades para o formulário de adição/edição
    @Published var locationName: String = ""
    @Published var locationAddress: String = ""
    @Published var locationIsActive: Bool = true
    @Published var locationColor: Color = .green

    // Cores predefinidas para seleção
    let predefinedColors: [Color] = [
        .green, .blue, .purple, .orange, .red, .pink, .yellow, .teal, .indigo, .cyan
    ]

    // Inicializador
    init(locationStore: LocationStore) {
        self.locationStore = locationStore
    }

    // Método para atualizar a referência ao LocationStore
    func updateLocationStore(_ locationStore: LocationStore) {
        self.locationStore = locationStore
        objectWillChange.send()
    }

    // MARK: - Computed Properties

    /// Retorna os locais filtrados com base no texto de pesquisa
    var filteredLocations: [Location] {
        if searchText.isEmpty {
            return locationStore.locations
        } else {
            return locationStore.locations.filter { $0.name.localizedCaseInsensitiveContains(searchText) }
        }
    }

    /// Verifica se o formulário é válido para salvar
    var isFormValid: Bool {
        return !locationName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }

    // MARK: - Métodos de Gerenciamento de Locais

    /// Adiciona um novo local
    func addLocation() {
        guard isFormValid else { return }

        let newLocation = Location(
            name: locationName.trimmingCharacters(in: .whitespacesAndNewlines),
            address: locationAddress.isEmpty ? nil : locationAddress.trimmingCharacters(in: .whitespacesAndNewlines),
            isActive: locationIsActive,
            colorHex: locationColor.toHex()
        )

        locationStore.addLocation(newLocation)
        resetForm()
    }

    /// Atualiza um local existente
    func updateLocation() {
        guard isFormValid, let location = selectedLocation else { return }

        let updatedLocation = Location(
            id: location.id,
            name: locationName.trimmingCharacters(in: .whitespacesAndNewlines),
            address: locationAddress.isEmpty ? nil : locationAddress.trimmingCharacters(in: .whitespacesAndNewlines),
            isActive: locationIsActive,
            colorHex: locationColor.toHex()
        )

        locationStore.updateLocation(updatedLocation)
        resetForm()
    }

    /// Remove um local
    func removeLocation(at indexSet: IndexSet) {
        locationStore.removeLocation(at: indexSet)
    }

    /// Remove um local específico
    func removeLocation(withID id: UUID) {
        locationStore.removeLocation(withID: id)
    }

    /// Alterna o estado ativo de um local
    func toggleLocationActive(_ location: Location) {
        locationStore.toggleLocationActive(location)
    }

    // MARK: - Métodos de Formulário

    /// Prepara o formulário para adicionar um novo local
    func prepareForAddLocation() {
        resetForm()
        isAddingLocation = true
        isEditingLocation = false
        selectedLocation = nil
    }

    /// Prepara o formulário para editar um local existente
    func prepareForEditLocation(_ location: Location) {
        resetForm()
        locationName = location.name
        locationAddress = location.address ?? ""
        locationIsActive = location.isActive
        locationColor = location.color
        selectedLocation = location
        isEditingLocation = true
        isAddingLocation = false
    }

    /// Salva o local (adiciona novo ou atualiza existente)
    func saveLocation() {
        if isAddingLocation {
            addLocation()
        } else if isEditingLocation {
            updateLocation()
        }

        isAddingLocation = false
        isEditingLocation = false
        selectedLocation = nil
    }

    /// Cancela a operação atual e reseta o formulário
    func cancelOperation() {
        isAddingLocation = false
        isEditingLocation = false
        selectedLocation = nil
        resetForm()
    }

    /// Reseta o formulário para os valores padrão
    private func resetForm() {
        locationName = ""
        locationAddress = ""
        locationIsActive = true
        locationColor = .green
    }

    // MARK: - Métodos de Utilidade

    /// Retorna o título apropriado para o formulário
    var formTitle: String {
        return isAddingLocation ? "Adicionar Local" : "Editar Local"
    }

    /// Retorna o texto do botão de ação
    var actionButtonText: String {
        return isAddingLocation ? "Adicionar" : "Salvar"
    }
}
