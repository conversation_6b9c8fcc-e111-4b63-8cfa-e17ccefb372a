import SwiftUI

struct NotesSection: View {
    @ObservedObject var formViewModel: DutyFormViewModel
    var body: some View {
        Section {
            VStack(spacing: 12) {
                HStack {
                    Image(systemName: "note.text")
                        .foregroundColor(.accentMedical)
                        .font(.system(size: 18))
                    Text("Observações")
                        .font(.headline)
                        .foregroundColor(.primary)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                ZStack(alignment: .topLeading) {
                    TextEditor(text: $formViewModel.notes)
                        .frame(minHeight: 100)
                        .padding(8)
                        .background(
                            RoundedRectangle(cornerRadius: 10)
                                .fill(Color.backgroundMedical.opacity(0.7))
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: 10)
                                .stroke(Color.secondary.opacity(0.3), lineWidth: 1)
                        )
                    if formViewModel.notes.isEmpty {
                        Text("Adicione informações adicionais sobre o plantão aqui...")
                            .foregroundColor(.secondary)
                            .padding(.horizontal, 12)
                            .padding(.top, 12)
                            .allowsHitTesting(false)
                    }
                }
            }
            .padding(.top, 8)
            .padding(.bottom, 12)
        }
    }
} 