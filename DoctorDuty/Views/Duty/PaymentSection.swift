import SwiftUI

struct PaymentSection: View {
    @ObservedObject var formViewModel: DutyFormViewModel
    var body: some View {
        Section {
            VStack(spacing: 12) {
                HStack {
                    Image(systemName: "dollarsign.circle.fill")
                        .foregroundColor(.accentMedical)
                        .font(.system(size: 18))
                    Text("Informações de Pagamento")
                        .font(.headline)
                        .foregroundColor(.primary)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                HStack(spacing: 12) {
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Image(systemName: "banknote.fill")
                                .foregroundColor(.accentMedical)
                                .font(.system(size: 14))
                            Text("Valor")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                        HStack(spacing: 0) {
                            Text(Locale.current.currencySymbol ?? "$")
                                .foregroundColor(.accentMedical)
                                .font(.headline)
                                .padding(.trailing, 4)
                            CurrencyTextField(value: $formViewModel.paymentAmount)
                                .font(.headline)
                                .frame(width: 100, height: 33)
                        }
                        .padding(.vertical, 8)
                        .padding(.horizontal, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color.backgroundMedical.opacity(0.7))
                        )
                        if let error = formViewModel.paymentError {
                            HStack {
                                Image(systemName: "exclamationmark.triangle.fill")
                                    .foregroundColor(.red)
                                    .font(.system(size: 12))
                                Text(error)
                                    .font(.caption)
                                    .foregroundColor(.red)
                            }
                        }
                    }
                    .frame(maxWidth: .infinity)
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Image(systemName: "checkmark.seal.fill")
                                .foregroundColor(.accentMedical)
                                .font(.system(size: 14))
                            Text("Status")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                        HStack(spacing: 12) {
                            Text(formViewModel.isPaid ? "Pago" : "Pendente")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(formViewModel.isPaid ? .paidColor : .unpaidColor)
                                .frame(width: 65, alignment: .leading)
                            Toggle("", isOn: $formViewModel.isPaid)
                                .labelsHidden()
                                .toggleStyle(SwitchToggleStyle(tint: .paidColor))
                                .fixedSize()
                        }
                        .padding(.vertical, 8)
                        .padding(.horizontal, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color.backgroundMedical.opacity(0.7))
                        )
                    }
                    .frame(maxWidth: .infinity)
                }
            }
            .padding(.vertical, 4)
        }
        .listSectionSeparator(.hidden)
    }
} 