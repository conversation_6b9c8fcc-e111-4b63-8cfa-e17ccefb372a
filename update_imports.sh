#!/bin/bash

# This script updates the import statements in Swift files to reflect the new folder structure

# Function to update imports in a file
update_imports() {
    local file=$1
    echo "Updating imports in $file"
    
    # Check if the file exists
    if [ ! -f "$file" ]; then
        echo "File $file does not exist"
        return
    fi
    
    # Update the file path in the file header comment
    sed -i '' -E "s|//  ([^/]+\.swift)|//  $(basename $file)|g" "$file"
}

# Update all Swift files in the project
find DoctorDuty -name "*.swift" -type f | while read file; do
    update_imports "$file"
done

echo "Import updates completed"
