//
//  Color+Extensions.swift
//  DoctorDuty
//
//  Created by Augment Agent on 01/05/25.
//

import SwiftUI

extension Color {
    // Cores principais do tema
    static let accentMedical = Color("AccentColor")
    static let backgroundMedical = Color("BackgroundColor")
    
    // Cores específicas para uso direto
    static let medicalGreen = Color(red: 0.4, green: 0.8, blue: 0.6)
    static let medicalBlue = Color(red: 0.4, green: 0.5, blue: 0.8)
    
    // Cores para tipos de plantão
    static let dayShiftBackground = Color.orange.opacity(0.2)
    static let nightShiftBackground = Color.indigo.opacity(0.2)
    
    // Cores para tipos de pessoa
    static let individualBackground = Color.purple.opacity(0.2)
    static let companyBackground = Color.blue.opacity(0.2)
    
    // Cores para status de pagamento
    static let paidColor = Color.medicalGreen
    static let unpaidColor = Color.red.opacity(0.8)
    
    // Cores para calendário
    static let calendarSelectedDay = Color.medicalGreen.opacity(0.3)
    static let calendarDayWithDuty = Color.medicalGreen.opacity(0.1)
}