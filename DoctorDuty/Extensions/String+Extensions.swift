//
//  String+Extensions.swift
//  DoctorDuty
//
//  Created by Augment Agent on 01/05/25.
//

import Foundation

extension String {
    /// Returns true if the string is empty or contains only whitespace
    var isBlank: Bool {
        return trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
    
    /// Returns the string with the first letter capitalized
    var capitalizingFirstLetter: String {
        return prefix(1).capitalized + dropFirst()
    }
    
    /// Capitalizes the first letter of the string
    mutating func capitalizeFirstLetter() {
        self = self.capitalizingFirstLetter
    }
    
    /// Returns true if the string contains only letters
    var containsOnlyLetters: Bool {
        return !isEmpty && range(of: "[^a-zA-Z]", options: .regularExpression) == nil
    }
    
    /// Returns true if the string contains only digits
    var containsOnlyDigits: Bool {
        return !isEmpty && range(of: "[^0-9]", options: .regularExpression) == nil
    }
    
    /// Returns true if the string is a valid email address
    var isValidEmail: Bool {
        let emailRegEx = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPred = NSPredicate(format:"SELF MATCHES %@", emailRegEx)
        return emailPred.evaluate(with: self)
    }
    
    /// Returns true if the string is a valid phone number
    var isValidPhoneNumber: Bool {
        let phoneRegEx = "^[0-9+]{8,}$"
        let phonePred = NSPredicate(format: "SELF MATCHES %@", phoneRegEx)
        return phonePred.evaluate(with: self)
    }
    
    /// Returns the string with all whitespace removed
    var removingWhitespace: String {
        return components(separatedBy: .whitespacesAndNewlines).joined()
    }
    
    /// Removes all whitespace from the string
    mutating func removeWhitespace() {
        self = self.removingWhitespace
    }
    
    /// Returns a currency value from a string, or nil if the string is not a valid currency
    var currencyValue: Double? {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.locale = Locale.current
        
        // Remove currency symbols and separators
        let cleanString = self.replacingOccurrences(of: formatter.currencySymbol, with: "")
            .replacingOccurrences(of: formatter.groupingSeparator, with: "")
            .replacingOccurrences(of: formatter.decimalSeparator, with: ".")
            .trimmingCharacters(in: .whitespacesAndNewlines)
        
        return Double(cleanString)
    }
}
