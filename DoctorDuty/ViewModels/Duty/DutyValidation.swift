import Foundation

struct DutyValidation {
    enum ValidationError: LocalizedError {
        case emptyLocation
        case invalidPayment
        case invalidRecurrence

        var errorDescription: String? {
            switch self {
            case .emptyLocation:
                return "O local é obrigatório"
            case .invalidPayment:
                return "O valor deve ser maior que zero"
            case .invalidRecurrence:
                return "A data final da recorrência deve ser após a data inicial"
            }
        }
    }

    static func validate(location: String, paymentAmount: Double, isRecurring: Bool, date: Date, recurrenceEndDate: Date) -> [ValidationError] {
        var errors: [ValidationError] = []
        if location.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            errors.append(.emptyLocation)
        }
        if paymentAmount <= 0 {
            errors.append(.invalidPayment)
        }
        if isRecurring && recurrenceEndDate <= date {
            errors.append(.invalidRecurrence)
        }
        return errors
    }
} 