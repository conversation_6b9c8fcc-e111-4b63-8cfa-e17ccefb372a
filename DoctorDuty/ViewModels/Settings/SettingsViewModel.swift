//
//  SettingsViewModel.swift
//  DoctorDuty
//
//  Created by Augment Agent on 01/05/25.
//

import SwiftUI
import Combine

class SettingsViewModel: ObservableObject {
    // Referências aos stores e viewmodels
    private(set) var locationStore: LocationStore
    private(set) var dutyViewModel: DutyViewModel

    // Propriedades publicadas para atualizações na UI
    @Published var showingAbout: Bool = false
    @Published var showingPaymentSettings: Bool = false

    // Configurações de pagamento
    @Published var defaultPaymentAmount: Double = 0.0
    @Published var defaultIsLegalEntity: Bool = false
    @Published var defaultCurrency: String = "BRL"

    // Configurações de notificações
    @Published var notificationsEnabled: Bool = true
    @Published var notificationTime: Int = 24 // Horas antes do plantão

    // Configurações de aparência
    @Published var useDarkMode: Bool = false
    @Published var useSystemTheme: Bool = true

    // Chaves para UserDefaults
    private let defaultPaymentAmountKey = "defaultPaymentAmount"
    private let defaultIsLegalEntityKey = "defaultIsLegalEntity"
    private let defaultCurrencyKey = "defaultCurrency"
    private let notificationsEnabledKey = "notificationsEnabled"
    private let notificationTimeKey = "notificationTime"
    private let useDarkModeKey = "useDarkMode"
    private let useSystemThemeKey = "useSystemTheme"

    // Inicializador
    init(locationStore: LocationStore, dutyViewModel: DutyViewModel) {
        self.locationStore = locationStore
        self.dutyViewModel = dutyViewModel
        loadSettings()
    }

    // MARK: - Métodos de Gerenciamento de Configurações

    /// Atualiza as referências para os stores e viewmodels
    func updateReferences(locationStore: LocationStore, dutyViewModel: DutyViewModel) {
        self.locationStore = locationStore
        self.dutyViewModel = dutyViewModel
        objectWillChange.send()
    }

    /// Carrega as configurações do UserDefaults
    private func loadSettings() {
        let defaults = UserDefaults.standard

        defaultPaymentAmount = defaults.double(forKey: defaultPaymentAmountKey)
        defaultIsLegalEntity = defaults.bool(forKey: defaultIsLegalEntityKey)
        defaultCurrency = defaults.string(forKey: defaultCurrencyKey) ?? "BRL"

        notificationsEnabled = defaults.bool(forKey: notificationsEnabledKey)
        notificationTime = defaults.integer(forKey: notificationTimeKey)
        if notificationTime == 0 {
            notificationTime = 24 // Valor padrão se não estiver definido
        }

        useDarkMode = defaults.bool(forKey: useDarkModeKey)
        useSystemTheme = defaults.bool(forKey: useSystemThemeKey)
        if (defaults.object(forKey: useSystemThemeKey).map({ _ in true }) == nil) {
            useSystemTheme = true // Valor padrão se não estiver definido
        }
    }

    /// Salva as configurações no UserDefaults
    func saveSettings() {
        let defaults = UserDefaults.standard

        defaults.set(defaultPaymentAmount, forKey: defaultPaymentAmountKey)
        defaults.set(defaultIsLegalEntity, forKey: defaultIsLegalEntityKey)
        defaults.set(defaultCurrency, forKey: defaultCurrencyKey)

        defaults.set(notificationsEnabled, forKey: notificationsEnabledKey)
        defaults.set(notificationTime, forKey: notificationTimeKey)

        defaults.set(useDarkMode, forKey: useDarkModeKey)
        defaults.set(useSystemTheme, forKey: useSystemThemeKey)
    }

    /// Reseta todas as configurações para os valores padrão
    func resetSettings() {
        defaultPaymentAmount = 0.0
        defaultIsLegalEntity = false
        defaultCurrency = "BRL"

        notificationsEnabled = true
        notificationTime = 24

        useDarkMode = false
        useSystemTheme = true

        saveSettings()
    }

    // MARK: - Métodos de Acesso

    /// Retorna o LocationViewModel para a tela de locais
    func createLocationViewModel() -> LocationViewModel {
        return LocationViewModel(locationStore: locationStore)
    }

    /// Formata o valor monetário de acordo com a moeda selecionada
    func formatCurrency(_ amount: Double) -> String {
        return dutyViewModel.formatCurrency(amount)
    }

    // MARK: - Métodos de Configuração de Pagamento

    /// Retorna as opções de moeda disponíveis
    var availableCurrencies: [String] {
        return ["BRL", "USD", "EUR", "GBP"]
    }

    /// Retorna o símbolo da moeda atual
    var currencySymbol: String {
        switch defaultCurrency {
        case "USD":
            return "$"
        case "EUR":
            return "€"
        case "GBP":
            return "£"
        default:
            return "R$"
        }
    }

    /// Retorna as opções de tempo para notificações
    var notificationTimeOptions: [(label: String, value: Int)] {
        return [
            ("30 minutos antes", 30),
            ("1 hora antes", 60),
            ("2 horas antes", 120),
            ("6 horas antes", 360),
            ("12 horas antes", 720),
            ("1 dia antes", 1440),
            ("2 dias antes", 2880)
        ]
    }

    // MARK: - Informações do Aplicativo

    /// Retorna a versão atual do aplicativo
    var appVersion: String {
        if let version = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String,
           let build = Bundle.main.infoDictionary?["CFBundleVersion"] as? String {
            return "\(version) (\(build))"
        }
        return "1.0.0"
    }

    /// Retorna o ano atual para o copyright
    var copyrightYear: String {
        let calendar = Calendar.current
        return String(calendar.component(.year, from: Date()))
    }
}
