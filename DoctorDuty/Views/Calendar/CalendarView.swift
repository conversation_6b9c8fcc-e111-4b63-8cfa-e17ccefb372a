//
//  CalendarView.swift
//  DoctorDuty
//
//  Created by Augment Agent on 01/05/25.
//

import SwiftUI

struct CalendarView: View {
    @EnvironmentObject var dutyViewModel: DutyViewModel
    @StateObject private var viewModel: CalendarViewModel
    @State private var showingAddDuty = false

    private let calendar = Calendar.current

    init() {
        // Inicializar com um viewModel temporário que será substituído pelo @EnvironmentObject
        let tempViewModel = DutyViewModel()
        self._viewModel = StateObject(wrappedValue: CalendarViewModel(dutyViewModel: tempViewModel))
    }

    var body: some View {
        NavigationStack {
            ZStack {
                Color("BackgroundColor")
                    .ignoresSafeArea()

                VStack(spacing: 0) {
                    // Indicador de carregamento
                    if dutyViewModel.isLoading {
                        LoadingView(state: dutyViewModel.loadingState)
                            .padding(.vertical, 8)
                    }

                    // Mensagem de erro, se houver
                    if let errorMessage = dutyViewModel.loadingError {
                        ErrorView(message: errorMessage) {
                            Task {
                                await dutyViewModel.loadDuties()
                            }
                        }
                        .padding(.vertical, 8)
                    }

                    // Calendário fixo no topo
                    VStack(spacing: 0) {
                        // Calendar header - Design moderno
                        ZStack {
                            // Fundo do cabeçalho
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.accentMedical.opacity(0.1))
                                .frame(height: 60)

                            HStack {
                                // Botão para mês anterior
                                Button(action: {
                                    withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                                        viewModel.previousMonth()
                                    }
                                }) {
                                    Image(systemName: "chevron.left")
                                        .font(.headline)
                                        .padding(10)
                                        .background(Circle().fill(Color.white.opacity(0.8)))
                                        .foregroundColor(.accentMedical)
                                        .shadow(color: Color.black.opacity(0.1), radius: 1, x: 0, y: 1)
                                }
                                .buttonStyle(ScaleButtonStyle())

                                Spacer()

                                // Nome do mês e botão para ir para hoje
                                VStack(spacing: 2) {
                                    Text(viewModel.currentMonthName)
                                        .font(.title3)
                                        .fontWeight(.bold)
                                        .foregroundColor(.primary)

                                    Button(action: {
                                        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                                            viewModel.goToToday()
                                        }
                                    }) {
                                        Text("Hoje")
                                            .font(.caption)
                                            .fontWeight(.medium)
                                            .foregroundColor(.accentMedical)
                                    }
                                    .buttonStyle(ScaleButtonStyle())
                                }

                                Spacer()

                                // Botão para próximo mês
                                Button(action: {
                                    withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                                        viewModel.nextMonth()
                                    }
                                }) {
                                    Image(systemName: "chevron.right")
                                        .font(.headline)
                                        .padding(10)
                                        .background(Circle().fill(Color.white.opacity(0.8)))
                                        .foregroundColor(.accentMedical)
                                        .shadow(color: Color.black.opacity(0.1), radius: 1, x: 0, y: 1)
                                }
                                .buttonStyle(ScaleButtonStyle())

                                // Botão para minimizar/maximizar
                                Button(action: {
                                    withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
                                        viewModel.toggleCalendarMinimized()
                                    }
                                }) {
                                    Image(systemName: viewModel.isCalendarMinimized ? "chevron.down" : "chevron.up")
                                        .font(.headline)
                                        .padding(10)
                                        .background(Circle().fill(Color.white.opacity(0.8)))
                                        .foregroundColor(.accentMedical)
                                        .shadow(color: Color.black.opacity(0.1), radius: 1, x: 0, y: 1)
                                        .padding(.leading, 4)
                                }
                                .buttonStyle(ScaleButtonStyle())
                            }
                            .padding(.horizontal, 16)
                        }
                        .padding(.horizontal, 8)
                        .padding(.top, 8)

                        // Conteúdo do calendário
                        VStack {
                            if !viewModel.isCalendarMinimized {
                                // Days of week header - Design melhorado
                                HStack(spacing: 0) {
                                    ForEach(viewModel.daysOfWeek, id: \.self) { day in
                                        Text(day)
                                            .font(.caption)
                                            .fontWeight(.semibold)
                                            .foregroundColor(.secondary)
                                            .frame(maxWidth: .infinity)
                                    }
                                }
                                .padding(.top, 16)
                                .padding(.bottom, 8)
                                .padding(.horizontal, 8)
                                .transition(AnyTransition.opacity.combined(with: .move(edge: .top)))

                                // Calendar grid - Design melhorado
                                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 7), spacing: 8) {
                                    ForEach(viewModel.daysInMonth(), id: \.self) { date in
                                        if let date = date {
                                            let hasduties = viewModel.hasDuties(on: date)
                                            let isToday = viewModel.isToday(date)
                                            let isSelected = viewModel.isSelectedDate(date)
                                            let isCurrentMonth = calendar.isDate(date, equalTo: viewModel.currentMonth, toGranularity: .month)

                                            Button(action: {
                                                withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                                                    viewModel.selectDate(date)
                                                }
                                                if hasduties {
                                                    // Show duty details
                                                } else {
                                                    showingAddDuty = true
                                                }
                                            }) {
                                                ZStack {
                                                    // Fundo do dia
                                                    if isSelected {
                                                        Circle()
                                                            .fill(Color.accentMedical)
                                                            .frame(width: 36, height: 36)
                                                    } else if isToday {
                                                        Circle()
                                                            .stroke(Color.accentMedical, lineWidth: 2)
                                                            .frame(width: 36, height: 36)
                                                    } else if hasduties {
                                                        Circle()
                                                            .fill(Color.accentMedical.opacity(0.1))
                                                            .frame(width: 36, height: 36)
                                                    }

                                                    VStack(spacing: 2) {
                                                        // Número do dia
                                                        Text("\(calendar.component(.day, from: date))")
                                                            .font(.system(size: 16))
                                                            .fontWeight(isToday || isSelected ? .bold : .medium)
                                                            .foregroundColor(
                                                                isSelected ? .white :
                                                                    (isToday ? .accentMedical :
                                                                        (isCurrentMonth ? .primary : .secondary.opacity(0.5)))
                                                            )

                                                        // Indicador de plantões
                                                        if hasduties {
                                                            Circle()
                                                                .fill(isSelected ? .white : .accentMedical)
                                                                .frame(width: 5, height: 5)
                                                                .opacity(isSelected ? 0.9 : 0.8)
                                                        }
                                                    }
                                                }
                                                .frame(height: 44)
                                                .frame(maxWidth: .infinity)
                                            }
                                            .buttonStyle(ScaleButtonStyle())
                                            .disabled(!isCurrentMonth)
                                        } else {
                                            Color.clear
                                                .frame(height: 44)
                                        }
                                    }
                                }
                                .padding(.horizontal, 8)
                                .padding(.bottom, 16)
                                .transition(AnyTransition.opacity.combined(with: .move(edge: .top)))
                            } else {
                                // Minimized calendar indicator - Design melhorado
                                HStack {
                                    VStack(alignment: .leading, spacing: 2) {
                                        Text("Data selecionada")
                                            .font(.caption)
                                            .foregroundColor(.secondary)

                                        Text(viewModel.formatDate(viewModel.selectedDate))
                                            .font(.headline)
                                            .foregroundColor(.primary)
                                    }

                                    Spacer()

                                    // Indicador visual do dia selecionado
                                    ZStack {
                                        Circle()
                                            .fill(Color.accentMedical.opacity(0.1))
                                            .frame(width: 40, height: 40)

                                        Text("\(calendar.component(.day, from: viewModel.selectedDate))")
                                            .font(.system(size: 18, weight: .bold))
                                            .foregroundColor(.accentMedical)
                                    }
                                }
                                .padding(.horizontal, 16)
                                .padding(.vertical, 12)
                                .transition(AnyTransition.opacity.combined(with: .move(edge: .bottom)))
                            }
                        }
                        .background(Color("BackgroundColor"))
                        .cornerRadius(16)
                        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
                        .padding(.horizontal, 8)
                    }
                    .padding(.bottom, 12)

                    // Lista rolável de plantões - Design melhorado
                    ScrollView {
                        VStack(spacing: 16) {
                            // Cabeçalho da seção de plantões
                            HStack {
                                VStack(alignment: .leading, spacing: 4) {
                                    // Título da seção
                                    Text("Plantões")
                                        .font(.headline)
                                        .foregroundColor(.primary)

                                    // Data selecionada
                                    HStack(spacing: 4) {
                                        Image(systemName: "calendar")
                                            .font(.caption)
                                            .foregroundColor(.accentMedical)

                                        Text(viewModel.formatDate(viewModel.selectedDate))
                                            .font(.subheadline)
                                            .foregroundColor(.secondary)
                                    }
                                }

                                Spacer()

                                // Botão para adicionar plantão
                                Button(action: {
                                    showingAddDuty = true
                                }) {
                                    HStack(spacing: 4) {
                                        Image(systemName: "plus")
                                            .font(.caption)

                                        Text("Adicionar")
                                            .font(.caption)
                                            .fontWeight(.medium)
                                    }
                                    .padding(.horizontal, 12)
                                    .padding(.vertical, 6)
                                    .background(Color.accentMedical)
                                    .foregroundColor(.white)
                                    .cornerRadius(20)
                                }
                                .buttonStyle(ScaleButtonStyle())
                            }
                            .padding(.horizontal, 16)
                            .padding(.top, 16)

                            // Lista de plantões
                            if !viewModel.dutiesForSelectedDate.isEmpty {
                                VStack(spacing: 12) {
                                    ForEach(viewModel.dutiesForSelectedDate) { duty in
                                        NavigationLink(destination: DutyDetailView(duty: duty)) {
                                            DutyCard(duty: duty)
                                                .padding(.horizontal, 16)
                                        }
                                        .buttonStyle(PlainButtonStyle())
                                    }
                                }
                                .padding(.bottom, 16)
                            } else {
                                // Estado vazio
                                VStack(spacing: 12) {
                                    Image(systemName: "calendar.badge.exclamationmark")
                                        .font(.system(size: 50))
                                        .foregroundColor(.secondary.opacity(0.6))
                                        .padding(.top, 20)

                                    Text("Nenhum plantão agendado")
                                        .font(.headline)
                                        .foregroundColor(.secondary)

                                    Text("Toque no botão adicionar para agendar um plantão para este dia.")
                                        .font(.subheadline)
                                        .foregroundColor(.secondary.opacity(0.8))
                                        .multilineTextAlignment(.center)
                                        .padding(.horizontal, 40)

                                    Button(action: {
                                        showingAddDuty = true
                                    }) {
                                        HStack {
                                            Image(systemName: "plus.circle.fill")
                                            Text("Adicionar Plantão")
                                        }
                                        .padding(.horizontal, 20)
                                        .padding(.vertical, 10)
                                        .background(Color.accentMedical)
                                        .foregroundColor(.white)
                                        .cornerRadius(10)
                                    }
                                    .buttonStyle(ScaleButtonStyle())
                                    .padding(.top, 8)
                                }
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, 30)
                            }
                        }
                    }
                    .background(Color("BackgroundColor"))
                    .cornerRadius(16, corners: [.topLeft, .topRight])
                    .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: -2)
                }
                .navigationTitle("Plantões Médicos")
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        HStack {
                            Button(action: {
                                showingAddDuty = true
                            }) {
                                Image(systemName: "plus")
                                    .foregroundColor(.accentMedical)
                            }

                            NavigationLink(destination: SettingsView().environmentObject(dutyViewModel.locationStore ?? LocationStore())) {
                                Image(systemName: "gear")
                                    .foregroundColor(.accentMedical)
                            }
                        }
                    }
                }
                .sheet(isPresented: $showingAddDuty) {
                    DutyFormView(date: viewModel.selectedDate)
                        .environmentObject(dutyViewModel)
                }
                .accentColor(.accentMedical)
                .onAppear {
                    // Atualizar o viewModel com o dutyViewModel correto do ambiente
                    if viewModel.dutyViewModel !== dutyViewModel {
                        viewModel.updateDutyViewModel(dutyViewModel)
                    }
                }
            }
        }
    }

    // Método para atualizar o ViewModel com o DutyViewModel correto
    private func updateDutyViewModel(_ dutyViewModel: DutyViewModel) {
        viewModel.updateDutyViewModel(dutyViewModel)
    }
}

// Estilo de botão personalizado com efeito de escala
struct ScaleButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.95 : 1)
            .animation(.spring(response: 0.2, dampingFraction: 0.7), value: configuration.isPressed)
    }
}

// Extensão para cantos arredondados personalizados
extension View {
    func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
        clipShape(RoundedCorner(radius: radius, corners: corners))
    }
}

struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners

    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(roundedRect: rect, byRoundingCorners: corners, cornerRadii: CGSize(width: radius, height: radius))
        return Path(path.cgPath)
    }
}

#Preview {
    let dutyViewModel = DutyViewModel()
    return CalendarView()
        .environmentObject(dutyViewModel)
}
