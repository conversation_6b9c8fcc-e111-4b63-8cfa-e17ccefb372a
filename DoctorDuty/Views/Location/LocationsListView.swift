//
//  LocationsListView.swift
//  DoctorDuty
//
//  Created by Augment Agent on 01/05/25.
//

import SwiftUI

struct LocationsListView: View {
    @EnvironmentObject var locationStore: LocationStore
    @StateObject private var viewModel: LocationViewModel

    init() {
        // Inicializar com um locationStore temporário que será substituído pelo @EnvironmentObject
        let tempLocationStore = LocationStore()
        self._viewModel = StateObject(wrappedValue: LocationViewModel(locationStore: tempLocationStore))
    }

    var body: some View {
        List {
            ForEach(viewModel.filteredLocations) { location in
                LocationRow(location: location, viewModel: viewModel) {
                    viewModel.prepareForEditLocation(location)
                }
            }
            .onDelete(perform: deleteLocations)
        }
        .navigationTitle("Hospitais e Clínicas")
        .searchable(text: $viewModel.searchText, prompt: "Buscar local")
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                    viewModel.prepareForAddLocation()
                }) {
                    Image(systemName: "plus")
                        .foregroundColor(.accentMedical)
                }
            }
        }
        .sheet(isPresented: $viewModel.isAddingLocation) {
            LocationFormView(viewModel: viewModel)
        }
        .sheet(isPresented: $viewModel.isEditingLocation) {
            if viewModel.selectedLocation != nil {
                LocationFormView(viewModel: viewModel)
            }
        }
        .onAppear {
            // Atualizar o viewModel com o locationStore correto do ambiente
            if viewModel.locationStore !== locationStore {
                viewModel.updateLocationStore(locationStore)
            }
        }
    }

    private func deleteLocations(at offsets: IndexSet) {
        viewModel.removeLocation(at: offsets)
    }
}

struct LocationRow: View {
    let location: Location
    let viewModel: LocationViewModel
    let onEdit: () -> Void

    var body: some View {
        HStack {
            // Indicador de cor
            Circle()
                .fill(location.color)
                .frame(width: 16, height: 16)
                .padding(.trailing, 4)

            VStack(alignment: .leading, spacing: 4) {
                Text(location.name)
                    .font(.headline)

                if let address = location.address, !address.isEmpty {
                    Text(address)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
            }

            Spacer()

            // Status indicator
            Circle()
                .fill(location.isActive ? Color.green : Color.red)
                .frame(width: 10, height: 10)

            // Edit button
            Button(action: onEdit) {
                Image(systemName: "pencil")
                    .foregroundColor(.accentMedical)
            }
            .buttonStyle(BorderlessButtonStyle())

            // Toggle active status
            Button(action: {
                viewModel.toggleLocationActive(location)
            }) {
                Image(systemName: location.isActive ? "checkmark.circle.fill" : "xmark.circle.fill")
                    .foregroundColor(location.isActive ? .green : .red)
            }
            .buttonStyle(BorderlessButtonStyle())
        }
        .padding(.vertical, 4)
    }
}

#Preview {
    let locationStore = LocationStore()

    return NavigationStack {
        LocationsListView()
            .environmentObject(locationStore)
    }
}
