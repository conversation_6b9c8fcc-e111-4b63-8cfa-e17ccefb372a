//
//  ContentView.swift
//  DoctorDuty
//
//  Created by <PERSON><PERSON><PERSON> on 01/05/25.
//

import SwiftUI

struct ContentView: View {
    @EnvironmentObject private var viewModel: DutyViewModel
    @StateObject private var locationStore = LocationStore()
    @State private var selectedTab = 0

    var body: some View {
        TabView(selection: $selectedTab) {
            CalendarView()
                .environmentObject(viewModel)
                .tabItem {
                    Label("Calendário", systemImage: "calendar")
                }
                .tag(0)

            DutyListView()
                .environmentObject(viewModel)
                .tabItem {
                    Label("Plantões", systemImage: "list.bullet")
                }
                .tag(1)

            StatisticsView()
                .environmentObject(viewModel)
                .tabItem {
                    Label("Estatísticas", systemImage: "chart.bar.fill")
                }
                .tag(2)

            SettingsView()
                .environmentObject(viewModel)
                .environmentObject(locationStore)
                .tabItem {
                    Label("Configurações", systemImage: "gear")
                }
                .tag(3)
        }
        .task {
            // Atualizar o viewModel com o locationStore
            viewModel.locationStore = locationStore

            // Carregar plantões (primeiro do cache, depois do servidor)
            await viewModel.loadDuties()
        }
        .refreshable {
            // Permite puxar para atualizar os dados
            await viewModel.loadDuties()
        }
    }
}

#Preview {
    let viewModel = DutyViewModel()
    let locationStore = LocationStore()
    viewModel.locationStore = locationStore

    return ContentView()
        .environmentObject(viewModel)
}
