//
//  DutyFormView.swift
//  DoctorDuty
//
//  Created by Augment Agent on 01/05/25.
//

import SwiftUI
import Combine
import UIKit

struct DutyFormView: View {
    @EnvironmentObject var viewModel: DutyViewModel
    @Environment(\.dismiss) private var dismiss
    @StateObject private var formViewModel: DutyFormViewModel
    @State private var showingNotificationPreview: Bool = false

    // Centraliza a lógica de inicialização do formViewModel
    private static func makeFormViewModel(date: Date? = nil, duty: Duty? = nil) -> DutyFormViewModel {
        let tempViewModel = DutyViewModel()
        if let duty = duty {
            return DutyFormViewModel(dutyViewModel: tempViewModel, duty: duty)
        } else if let date = date {
            return DutyFormViewModel(dutyViewModel: tempViewModel, date: date)
        } else {
            return DutyFormViewModel(dutyViewModel: tempViewModel, date: Date())
        }
    }

    init(date: Date) {
        _formViewModel = StateObject(wrappedValue: Self.makeFormViewModel(date: date))
    }

    init(duty: Duty, isNewDuty: Bool = false) {
        _formViewModel = StateObject(wrappedValue: Self.makeFormViewModel(duty: duty))
    }

    init(duty: Binding<Duty>, isNewDuty: Bool = false) {
        _formViewModel = StateObject(wrappedValue: Self.makeFormViewModel(duty: duty.wrappedValue))
    }

    var body: some View {
        NavigationStack {
            ZStack {
                Color.backgroundMedical
                    .ignoresSafeArea()

                Form {
                    locationSection
                    infoSection
                    paymentSection
                    advancedOptionsSection
                    notesSection
                }
                .scrollContentBackground(.hidden)
                .background(Color.backgroundMedical)
                .listStyle(.insetGrouped)
                .navigationTitle("Plantão Médico")
                .accentColor(.medicalGreen)
                .toolbar {
                    ToolbarItem(placement: .navigationBarLeading) {
                        Button(action: { dismiss() }) {
                            HStack {
                                Image(systemName: "xmark.circle.fill")
                            }
                            .foregroundColor(.secondary)
                        }
                    }
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button(action: {
                            if formViewModel.saveDuty() {
                                dismiss()
                            }
                        }) {
                            HStack {
                                Text("Salvar")
                                Image(systemName: "checkmark.circle.fill")
                            }
                            .foregroundColor(formViewModel.isFormValid ? .accentMedical : .gray)
                        }
                        .disabled(!formViewModel.isFormValid)
                    }
                }
            }
        }
        .onAppear {
            formViewModel.dutyViewModel = viewModel
        }
        .animation(.easeInOut(duration: 0.3), value: formViewModel.showAdvancedOptions)
        .animation(.easeInOut(duration: 0.3), value: formViewModel.isRecurring)
        .animation(.easeInOut(duration: 0.3), value: formViewModel.needsInvoiceReminder)
        .animation(.easeInOut(duration: 0.3), value: showingNotificationPreview)
    }

    // MARK: - Seções como propriedades computadas
    private var locationSection: some View {
        LocationSection(formViewModel: formViewModel)
    }
    private var infoSection: some View {
        InfoSection(formViewModel: formViewModel)
    }
    private var paymentSection: some View {
        PaymentSection(formViewModel: formViewModel)
    }
    private var advancedOptionsSection: some View {
        AdvancedOptionsSection(formViewModel: formViewModel, showingNotificationPreview: $showingNotificationPreview)
    }
    private var notesSection: some View {
        NotesSection(formViewModel: formViewModel)
    }
}

#Preview {
    let viewModel = DutyViewModel()
    viewModel.locationStore = LocationStore()
    return DutyFormView(date: Date())
        .environmentObject(viewModel)
}
