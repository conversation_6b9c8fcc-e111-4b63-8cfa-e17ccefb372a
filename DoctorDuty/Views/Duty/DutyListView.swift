//
//  DutyListView.swift
//  DoctorDuty
//
//  Created by Augment Agent on 01/05/25.
//

import SwiftUI

struct DutyListView: View {
    @EnvironmentObject var viewModel: DutyViewModel
    @StateObject private var listViewModel: DutyListViewModel
    @State private var showingAddDuty = false
    @State private var showingFilters = false
    @State private var searchText = ""

    init() {
        // Initialize with a temporary viewModel that will be replaced by the @EnvironmentObject
        let tempViewModel = DutyViewModel()
        self._listViewModel = StateObject(wrappedValue: DutyListViewModel(dutyViewModel: tempViewModel))
    }

    var body: some View {
        NavigationStack {
            VStack(spacing: 0) {
                // Indicador de carregamento
                if viewModel.isLoading {
                    LoadingView(state: viewModel.loadingState)
                        .padding(.vertical, 8)
                }

                // Mensagem de erro, se houver
                if let errorMessage = viewModel.loadingError {
                    ErrorView(message: errorMessage) {
                        Task {
                            await viewModel.loadDuties()
                        }
                    }
                    .padding(.vertical, 8)
                }

                // Filter bar
//                FilterBar(showingFilters: $showingFilters)

                // Active filters display
                if !listViewModel.selectedLocations.isEmpty ||
                   !listViewModel.showDayShifts ||
                   !listViewModel.showNightShifts ||
                   !listViewModel.showPaid ||
                   !listViewModel.showUnpaid {
                    ActiveFiltersView(listViewModel: listViewModel)
                }

                // Search bar
                SearchBar(text: $searchText)
                    .padding(.horizontal)
                    .onChange(of: searchText) { newValue, _ in
                        listViewModel.searchText = newValue
                    }

                // Check if we need to show empty state
                if listViewModel.filteredDuties.isEmpty {
                    // Empty state view
                    EmptyStateView(
                        type: viewModel.dutyStore.duties.isEmpty ? .noDuties : .noFilterResults,
                        action: {
                            if viewModel.dutyStore.duties.isEmpty {
                                showingAddDuty = true
                            } else {
                                listViewModel.resetAllFilters()
                            }
                        }
                    )
                    .transition(.opacity)
                    .animation(.easeInOut, value: listViewModel.filteredDuties.isEmpty)
                } else {
                    // Duties list
                    List {
                        // Summary section
                        Section(header: Text("Resumo")) {
                            SummaryRow(title: "Total", value: listViewModel.totalFilteredAmount, listViewModel: listViewModel)
                            SummaryRow(title: "Pago", value: listViewModel.paidFilteredAmount, listViewModel: listViewModel)
                            SummaryRow(title: "A receber", value: listViewModel.unpaidFilteredAmount, listViewModel: listViewModel)
                        }

                        // Duties section
                        Section(header: Text("Plantões")) {
                            ForEach(listViewModel.filteredDuties) { duty in
                                NavigationLink(destination: DutyDetailView(duty: duty)) {
                                    DutyCard(duty: duty)
                                        .padding(.vertical, 4)
                                }
                            }
                            .onDelete(perform: deleteDuties)
                        }
                    }
                    .listStyle(InsetGroupedListStyle())
                    .transition(.opacity)
                    .animation(.easeInOut, value: !listViewModel.filteredDuties.isEmpty)
                }
            }
            .navigationTitle("Plantões")
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    NavigationLink(destination: SettingsView().environmentObject(viewModel.locationStore ?? LocationStore())) {
                        Image(systemName: "gear")
                            .foregroundColor(.accentMedical)
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    HStack {
                        Button(action: {
                            showingFilters = true
                        }) {
                            Image(systemName: "line.3.horizontal.decrease.circle")
                                .foregroundColor(.accentMedical)
                        }

                        Button(action: {
                            showingAddDuty = true
                        }) {
                            Image(systemName: "plus")
                                .foregroundColor(.accentMedical)
                        }
                    }
                }
            }
            .sheet(isPresented: $showingAddDuty) {
                DutyFormView(date: Date())
            }
            .sheet(isPresented: $showingFilters) {
                FilterView(listViewModel: listViewModel)
            }
            .onAppear {
                // Update the listViewModel with the correct dutyViewModel from the environment
                if listViewModel.dutyViewModel !== viewModel {
                    listViewModel.updateDutyViewModel(viewModel)
                }
            }
        }
    }

    private func deleteDuties(at offsets: IndexSet) {
        listViewModel.deleteDuties(at: offsets)
    }
}

// MARK: - Supporting Views

struct FilterBar: View {
    @Binding var showingFilters: Bool

    var body: some View {
        Button(action: {
            showingFilters = true
        }) {
            HStack {
                Image(systemName: "line.3.horizontal.decrease.circle")
                Text("Filtrar Plantões")
                Spacer()
                Image(systemName: "chevron.down")
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(8)
            .padding(.horizontal)
            .padding(.top, 8)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct ActiveFiltersView: View {
    @ObservedObject var listViewModel: DutyListViewModel

    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack {
                // Location filters
                ForEach(Array(listViewModel.selectedLocations), id: \.self) { location in
                    FilterChip(
                        label: location,
                        color: listViewModel.colorForLocation(location),
                        onRemove: { listViewModel.toggleLocation(location) }
                    )
                }

                // Shift type filters
                if !listViewModel.showDayShifts {
                    FilterChip(
                        label: "Sem Diurnos",
                        color: .orange,
                        onRemove: { listViewModel.toggleDayShifts() }
                    )
                }

                if !listViewModel.showNightShifts {
                    FilterChip(
                        label: "Sem Noturnos",
                        color: .blue,
                        onRemove: { listViewModel.toggleNightShifts() }
                    )
                }

                // Payment status filters
                if !listViewModel.showPaid {
                    FilterChip(
                        label: "Sem Pagos",
                        color: .green,
                        onRemove: { listViewModel.togglePaidDuties() }
                    )
                }

                if !listViewModel.showUnpaid {
                    FilterChip(
                        label: "Sem Não Pagos",
                        color: .red,
                        onRemove: { listViewModel.toggleUnpaidDuties() }
                    )
                }

                // Clear all button
                Button(action: {
                    listViewModel.resetAllFilters()
                }) {
                    Text("Limpar Filtros")
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.accentMedical.opacity(0.2))
                        .foregroundColor(.accentMedical)
                        .cornerRadius(8)
                }
            }
            .padding(.horizontal)
        }
        .padding(.bottom, 8)
    }
}

struct FilterChip: View {
    let label: String
    let color: Color
    let onRemove: () -> Void

    var body: some View {
        HStack(spacing: 4) {
            Circle()
                .fill(color)
                .frame(width: 8, height: 8)

            Text(label)
                .font(.caption)

            Button(action: onRemove) {
                Image(systemName: "xmark.circle.fill")
                    .font(.caption)
            }
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

struct SearchBar: View {
    @Binding var text: String

    var body: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.gray)

            TextField("Buscar plantões", text: $text)
                .autocapitalization(.none)
                .disableAutocorrection(true)

            if !text.isEmpty {
                Button(action: {
                    text = ""
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.gray)
                }
            }
        }
        .padding(8)
        .background(Color(.systemGray6))
        .cornerRadius(8)
        .padding(.vertical, 8)
    }
}

struct SummaryRow: View {
    let title: String
    let value: Double
    let listViewModel: DutyListViewModel

    var body: some View {
        HStack {
            Text(title)
            Spacer()
            Text(listViewModel.formatCurrency(value))
                .fontWeight(.bold)
        }
    }
}

#Preview {
    DutyListView()
        .environmentObject(DutyViewModel())
}
