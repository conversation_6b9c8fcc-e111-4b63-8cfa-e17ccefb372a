//
//  DutyFormViewModel.swift
//  DoctorDuty
//
//  Created by Augment Agent on 01/05/25.
//

import Foundation
import SwiftUI

class DutyFormViewModel: ObservableObject {
    // Reference to the main ViewModel
    var dutyViewModel: DutyViewModel

    // Form state
    @Published var location: String = ""
    @Published var selectedLocation: String = ""
    @Published var showingAddLocationAlert: Bool = false
    @Published var newLocationName: String = ""
    @Published var date: Date = Date()
    @Published var paymentAmount: Double = 0.0
    @Published var isPaid: Bool = false
    @Published var notes: String = ""
    @Published var isLegalEntity: Bool = false {
        didSet {
            // Se não for PJ, desativar o lembrete de nota fiscal
            if !isLegalEntity {
                needsInvoiceReminder = false
            }
        }
    }
    @Published var isNightShift: Bool = false

    // Advanced options
    @Published var showAdvancedOptions: Bool = false
    @Published var isRecurring: Bool = false
    @Published var recurrenceType: RecurrenceType = .weekly
    @Published var recurrenceEndDate: Date = Date().addingTimeInterval(86400 * 30) // Default to 30 days

    // Invoice reminder options
    @Published var needsInvoiceReminder: Bool = false
    @Published var invoiceReminderDays: Int = 30 // Default to 30 days

    // Form state
    private let isNewDuty: Bool
    private let dutyId: UUID

    // Validation
    @Published var locationError: String? = nil
    @Published var paymentError: String? = nil
    @Published var recurrenceError: String? = nil

    init(dutyViewModel: DutyViewModel, duty: Duty? = nil) {
        self.dutyViewModel = dutyViewModel

        if let duty = duty {
            // Editing existing duty
            self.isNewDuty = false
            self.dutyId = duty.id

            // Initialize form fields with duty values
            self.location = duty.location
            self.selectedLocation = duty.location
            self.date = duty.date
            self.paymentAmount = duty.paymentAmount
            self.isPaid = duty.isPaid
            self.notes = duty.notes ?? ""
            self.isLegalEntity = duty.isLegalEntity
            self.isNightShift = duty.isNightShift
            self.isRecurring = duty.isRecurring

            if let recurrenceType = duty.recurrenceType {
                self.recurrenceType = recurrenceType
            }

            if let recurrenceEndDate = duty.recurrenceEndDate {
                self.recurrenceEndDate = recurrenceEndDate
            }

            // Carregar configurações de alerta de nota fiscal
            self.needsInvoiceReminder = duty.needsInvoiceReminder
            if let reminderDays = duty.invoiceReminderDays {
                self.invoiceReminderDays = reminderDays
            }

            // Se o local já existe na lista de locais salvos, selecioná-lo
            if !duty.location.isEmpty && dutyViewModel.savedLocations.contains(duty.location) {
                self.selectedLocation = duty.location
            }
        } else {
            // Creating new duty
            self.isNewDuty = true
            self.dutyId = UUID()

            // Se houver locais salvos, selecionar o primeiro como padrão
            if let firstLocation = dutyViewModel.savedLocations.first {
                self.selectedLocation = firstLocation
                self.location = firstLocation
            }
        }
    }

    // Initialize with a specific date (for creating from calendar)
    init(dutyViewModel: DutyViewModel, date: Date) {
        self.dutyViewModel = dutyViewModel
        self.isNewDuty = true
        self.dutyId = UUID()
        self.date = date

        // Se houver locais salvos, selecionar o primeiro como padrão
        if let firstLocation = dutyViewModel.savedLocations.first {
            self.selectedLocation = firstLocation
            self.location = firstLocation
        }
    }

    // MARK: - Validation

    func validateForm() -> Bool {
        let errors = DutyValidation.validate(
            location: location,
            paymentAmount: paymentAmount,
            isRecurring: isRecurring,
            date: date,
            recurrenceEndDate: recurrenceEndDate
        )
        locationError = nil
        paymentError = nil
        recurrenceError = nil
        for error in errors {
            switch error {
            case .emptyLocation:
                locationError = error.errorDescription
            case .invalidPayment:
                paymentError = error.errorDescription
            case .invalidRecurrence:
                recurrenceError = error.errorDescription
            }
        }
        return errors.isEmpty
    }

    // MARK: - Location Management

    /// Constante para identificar a opção "Adicionar Novo Local"
    let addNewLocationOption = "Adicionar Novo Local..."

    /// Retorna a lista de locais disponíveis para seleção
    var availableLocations: [String] {
        var locations: [String]

        // Usar locais do LocationStore se disponível
        if let locationStore = dutyViewModel.locationStore {
            locations = locationStore.locations
                .filter { $0.isActive }
                .map { $0.name }
                .sorted()
        } else {
            locations = dutyViewModel.savedLocations.sorted()
        }

        locations.append(addNewLocationOption)
        return locations
    }

    /// Retorna a cor associada a um local
    func colorForLocation(_ locationName: String) -> Color {
        if let locationStore = dutyViewModel.locationStore,
           let location = locationStore.locations.first(where: { $0.name == locationName }) {
            return location.color
        }
        return .accentMedical
    }

    /// Seleciona um local da lista ou mostra o alerta para adicionar um novo
    func selectLocation(_ locationName: String) {
        if locationName == addNewLocationOption {
            showingAddLocationAlert = true
            newLocationName = ""
        } else {
            selectedLocation = locationName
            location = locationName
        }
    }

    /// Adiciona um novo local
    func addNewLocation() {
        guard !newLocationName.isBlank else { return }

        dutyViewModel.addLocation(newLocationName)
        selectedLocation = newLocationName
        location = newLocationName
        showingAddLocationAlert = false
    }

    // MARK: - Actions

    func saveDuty() -> Bool {
        // Validate form before saving
        guard validateForm() else {
            return false
        }
        let finalLocation = selectedLocation.isEmpty ? location : selectedLocation
        if !finalLocation.isEmpty && !dutyViewModel.savedLocations.contains(finalLocation) {
            dutyViewModel.addLocation(finalLocation)
        }
        let duty = Duty(
            id: dutyId,
            date: date,
            location: finalLocation,
            paymentAmount: paymentAmount,
            isPaid: isPaid,
            notes: notes.isEmpty ? nil : notes,
            isLegalEntity: isLegalEntity,
            isNightShift: isNightShift,
            isRecurring: isRecurring,
            recurrenceType: isRecurring ? recurrenceType : nil,
            recurrenceEndDate: isRecurring ? recurrenceEndDate : nil,
            needsInvoiceReminder: needsInvoiceReminder,
            invoiceReminderDays: needsInvoiceReminder ? invoiceReminderDays : nil
        )
        if isNewDuty {
            dutyViewModel.addDuty(duty)
            if isRecurring {
                dutyViewModel.createRecurringDuties(basedOn: duty)
            }
        } else {
            dutyViewModel.updateDuty(duty)
            NotificationService.shared.cancelNotifications(for: duty.id)
        }
        if duty.isLegalEntity && duty.needsInvoiceReminder {
            NotificationService.shared.scheduleInvoiceReminder(for: duty)
        }
        return true
    }

    // MARK: - Helper Methods

    func formattedCurrency(_ value: Double) -> String {
        return FormattingHelper.formatCurrency(value)
    }

    var formattedDate: String {
        return FormattingHelper.formatMediumDate(date)
    }

    var formattedRecurrenceEndDate: String {
        return FormattingHelper.formatMediumDate(recurrenceEndDate)
    }

    var isFormValid: Bool {
        return !location.isBlank && paymentAmount > 0
    }
}
