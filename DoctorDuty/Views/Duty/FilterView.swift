//
//  FilterView.swift
//  DoctorDuty
//
//  Created by Augment Agent on 01/05/25.
//

import SwiftUI

struct FilterView: View {
    @Environment(\.dismiss) private var dismiss
    @ObservedObject var listViewModel: DutyListViewModel
    
    var body: some View {
        NavigationStack {
            Form {
                // Location filters
                Section(header: Text("Hospitais e Clínicas")) {
                    if listViewModel.availableLocations.isEmpty {
                        Text("Nenhum local cadastrado")
                            .foregroundColor(.secondary)
                    } else {
                        ForEach(listViewModel.availableLocations, id: \.self) { location in
                            Button(action: {
                                listViewModel.toggleLocation(location)
                            }) {
                                HStack {
                                    // Color indicator
                                    Circle()
                                        .fill(listViewModel.colorForLocation(location))
                                        .frame(width: 16, height: 16)
                                        .padding(.trailing, 4)
                                    
                                    Text(location)
                                    
                                    Spacer()
                                    
                                    if listViewModel.selectedLocations.contains(location) {
                                        Image(systemName: "checkmark")
                                            .foregroundColor(.accentMedical)
                                    }
                                }
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                        
                        Button(action: {
                            listViewModel.clearLocationFilters()
                        }) {
                            Text("Limpar seleção de locais")
                                .foregroundColor(.accentMedical)
                        }
                    }
                }
                
                // Shift type filters
                Section(header: Text("Tipo de Plantão")) {
                    Toggle(isOn: $listViewModel.showDayShifts) {
                        HStack {
                            Image(systemName: "sun.max.fill")
                                .foregroundColor(.orange)
                            Text("Plantões Diurnos")
                        }
                    }
                    .toggleStyle(SwitchToggleStyle(tint: .accentMedical))
                    
                    Toggle(isOn: $listViewModel.showNightShifts) {
                        HStack {
                            Image(systemName: "moon.fill")
                                .foregroundColor(.blue)
                            Text("Plantões Noturnos")
                        }
                    }
                    .toggleStyle(SwitchToggleStyle(tint: .accentMedical))
                }
                
                // Payment status filters
                Section(header: Text("Status de Pagamento")) {
                    Toggle(isOn: $listViewModel.showPaid) {
                        HStack {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                            Text("Plantões Pagos")
                        }
                    }
                    .toggleStyle(SwitchToggleStyle(tint: .accentMedical))
                    
                    Toggle(isOn: $listViewModel.showUnpaid) {
                        HStack {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(.red)
                            Text("Plantões Não Pagos")
                        }
                    }
                    .toggleStyle(SwitchToggleStyle(tint: .accentMedical))
                }
                
                // Reset all filters
                Section {
                    Button(action: {
                        listViewModel.resetAllFilters()
                        dismiss()
                    }) {
                        HStack {
                            Spacer()
                            Text("Limpar Todos os Filtros")
                                .foregroundColor(.red)
                            Spacer()
                        }
                    }
                }
            }
            .navigationTitle("Filtrar Plantões")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancelar") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Aplicar") {
                        dismiss()
                    }
                    .fontWeight(.bold)
                }
            }
        }
    }
}

#Preview {
    FilterView(listViewModel: DutyListViewModel(dutyViewModel: DutyViewModel()))
}
