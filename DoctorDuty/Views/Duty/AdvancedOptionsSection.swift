import SwiftUI

struct AdvancedOptionsSection: View {
    @ObservedObject var formViewModel: DutyFormViewModel
    @Binding var showingNotificationPreview: Bool
    var body: some View {
        Section {
            VStack(spacing: 12) {
                HStack {
                    Image(systemName: "repeat.circle.fill")
                        .foregroundColor(.accentMedical)
                        .font(.system(size: 18))
                    Text("Opções Avançadas")
                        .font(.headline)
                        .foregroundColor(.primary)
                    Spacer()
                    Button(action: {
                        withAnimation {
                            formViewModel.showAdvancedOptions.toggle()
                        }
                    }) {
                        Image(systemName: formViewModel.showAdvancedOptions ? "chevron.up.circle.fill" : "chevron.down.circle.fill")
                            .foregroundColor(.accentMedical)
                            .font(.system(size: 18))
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                if formViewModel.showAdvancedOptions {
                    HStack {
                        Toggle(isOn: $formViewModel.isRecurring) {
                            HStack {
                                Image(systemName: "arrow.triangle.2.circlepath")
                                    .foregroundColor(.accentMedical)
                                    .font(.system(size: 14))
                                Text("Plantão Recorrente")
                                    .font(.subheadline)
                            }
                        }
                        .toggleStyle(SwitchToggleStyle(tint: .accentMedical))
                    }
                    .padding(.top, 4)
                    if formViewModel.isRecurring {
                        HStack(spacing: 12) {
                            VStack(alignment: .leading, spacing: 4) {
                                HStack {
                                    Image(systemName: "calendar.badge.clock")
                                        .foregroundColor(.accentMedical)
                                        .font(.system(size: 14))
                                    Text("Frequência")
                                        .font(.subheadline)
                                        .foregroundColor(.secondary)
                                }
                                Picker("", selection: $formViewModel.recurrenceType) {
                                    ForEach(RecurrenceType.allCases, id: \.self) { type in
                                        Text(type.rawValue).tag(type)
                                    }
                                }
                                .pickerStyle(.menu)
                                .accentColor(.accentMedical)
                                .padding(.vertical, -1)
                                .padding(.horizontal, 12)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(Color.backgroundMedical.opacity(0.7))
                                )
                            }
                            .frame(maxWidth: .infinity)
                            VStack(alignment: .leading, spacing: 4) {
                                HStack {
                                    Image(systemName: "calendar.badge.exclamationmark")
                                        .foregroundColor(.accentMedical)
                                        .font(.system(size: 14))
                                    Text("Data Final")
                                        .font(.subheadline)
                                        .foregroundColor(.secondary)
                                }
                                DatePicker("", selection: $formViewModel.recurrenceEndDate, displayedComponents: .date)
                                    .datePickerStyle(.compact)
                                    .labelsHidden()
                                    .accentColor(.accentMedical)
                            }
                            .frame(maxWidth: .infinity)
                        }
                        .transition(.opacity)
                    }
                    if formViewModel.isLegalEntity {
                        HStack {
                            Toggle(isOn: $formViewModel.needsInvoiceReminder) {
                                HStack {
                                    Image(systemName: "bell.badge")
                                        .foregroundColor(.accentMedical)
                                        .font(.system(size: 14))
                                    Text("Lembrete para Nota Fiscal (PJ)")
                                        .font(.subheadline)
                                }
                            }
                            .toggleStyle(SwitchToggleStyle(tint: .accentMedical))
                        }
                        .padding(.top, 8)
                        if formViewModel.needsInvoiceReminder {
                            VStack(alignment: .leading, spacing: 4) {
                                HStack {
                                    Image(systemName: "calendar.badge.clock")
                                        .foregroundColor(.accentMedical)
                                        .font(.system(size: 14))
                                    Text("Prazo para emissão")
                                        .font(.subheadline)
                                        .foregroundColor(.secondary)
                                }
                                Picker("", selection: $formViewModel.invoiceReminderDays) {
                                    Text("15 dias").tag(15)
                                    Text("30 dias").tag(30)
                                    Text("45 dias").tag(45)
                                }
                                .pickerStyle(.segmented)
                                .padding(.vertical, 4)
                            }
                            .transition(.opacity)
                        }
                    }
                }
            }
            .padding(.vertical, 4)
        }
    }
} 