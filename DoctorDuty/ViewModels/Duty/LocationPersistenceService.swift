import Foundation

protocol LocationPersistence {
    func loadSavedLocations() -> [String]
    func saveSavedLocations(_ locations: [String])
}

class LocationPersistenceService: LocationPersistence {
    private let key = "SavedLocations"
    func loadSavedLocations() -> [String] {
        if let savedData = UserDefaults.standard.array(forKey: key) as? [String] {
            return savedData
        }
        return []
    }
    func saveSavedLocations(_ locations: [String]) {
        UserDefaults.standard.set(locations, forKey: key)
    }
} 