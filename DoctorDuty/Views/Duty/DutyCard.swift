//
//  DutyCard.swift
//  DoctorDuty
//
//  Created by Augment Agent on 01/05/25.
//

import SwiftUI

struct DutyCard: View {
    let duty: Duty
    @EnvironmentObject var viewModel: DutyViewModel
    
    var body: some View {
        VStack(spacing: 0) {
            // Cabeçalho com local e indicador de status
            HStack {
                // Indicador de cor do local
                Circle()
                    .fill(viewModel.colorForLocation(duty.location))
                    .frame(width: 12, height: 12)
                    .padding(.trailing, 2)
                
                Text(duty.location)
                    .font(.headline)
                    .lineLimit(1)
                
                Spacer()
                
                // Indicador de pagamento
                if duty.isPaid {
                    Text("PAGO")
                        .font(.caption2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(Color.paidColor)
                        .cornerRadius(4)
                }
            }
            .padding(.horizontal, 12)
            .padding(.top, 12)
            .padding(.bottom, 8)
            
            Divider()
                .padding(.horizontal, 12)
            
            // Conteúdo principal
            HStack(alignment: .top, spacing: 12) {
                // Coluna esquerda - Data e detalhes
                VStack(alignment: .leading, spacing: 8) {
                    // Data
                    HStack {
                        Image(systemName: "calendar")
                            .foregroundColor(.accentMedical)
                            .font(.system(size: 14))
                        
                        Text(dateFormatter.string(from: duty.date))
                            .font(.subheadline)
                            .foregroundColor(.primary)
                    }
                    
                    // Badges de tipo
                    HStack(spacing: 8) {
                        // Tipo de plantão (Diurno/Noturno)
                        HStack(spacing: 4) {
                            Image(systemName: duty.isNightShift ? "moon.stars.fill" : "sun.max.fill")
                                .foregroundColor(duty.isNightShift ? .white : .yellow)
                                .font(.system(size: 12))
                            
                            Text(duty.isNightShift ? "Noturno" : "Diurno")
                                .font(.caption)
                                .foregroundColor(duty.isNightShift ? .white : .black)
                        }
                        .padding(.horizontal, 6)
                        .padding(.vertical, 3)
                        .background(duty.isNightShift ? Color.nightShiftBackground : Color.dayShiftBackground)
                        .cornerRadius(4)
                        
                        // Tipo de pessoa (Física/Jurídica)
                        HStack(spacing: 4) {
                            Image(systemName: duty.isLegalEntity ? "building.2.fill" : "person.fill")
                                .font(.system(size: 12))
                            
                            Text(duty.isLegalEntity ? "PJ" : "PF")
                                .font(.caption)
                        }
                        .padding(.horizontal, 6)
                        .padding(.vertical, 3)
                        .background(duty.isLegalEntity ? Color.companyBackground : Color.individualBackground)
                        .cornerRadius(4)
                    }
                }
                
                Spacer()
                
                // Coluna direita - Valor
                VStack(alignment: .trailing, spacing: 4) {
                    Text(currencyFormatter.string(from: NSNumber(value: duty.paymentAmount)) ?? "$0.00")
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(duty.isPaid ? Color.paidColor : .primary)
                    
                    if !duty.isPaid {
                        Text("Pendente")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            
            // Notas (se existirem)
            if let notes = duty.notes, !notes.isEmpty {
                Divider()
                    .padding(.horizontal, 12)
                
                HStack {
                    Image(systemName: "text.quote")
                        .foregroundColor(.secondary)
                        .font(.system(size: 14))
                    
                    Text(notes)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                    
                    Spacer()
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
            }
        }
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.backgroundMedical.opacity(0.7))
                .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.gray.opacity(0.2), lineWidth: 1)
        )
    }
    
    private var dateFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        return formatter
    }
    
    private var currencyFormatter: NumberFormatter {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.locale = Locale.current
        return formatter
    }
}

#Preview {
    DutyCard(duty: Duty(
        date: Date(),
        location: "Hospital São Lucas",
        paymentAmount: 1250.00,
        isPaid: false,
        notes: "Plantão de emergência",
        isLegalEntity: true,
        isNightShift: true
    ))
    .environmentObject(DutyViewModel())
    .padding()
}
