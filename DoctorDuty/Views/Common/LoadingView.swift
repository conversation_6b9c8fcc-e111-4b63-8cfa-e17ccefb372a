//
//  LoadingView.swift
//  DoctorDuty
//
//  Created by Augment Agent on 01/05/25.
//

import SwiftUI

struct LoadingView: View {
    let state: DutyViewModel.LoadingState
    
    var body: some View {
        HStack(spacing: 12) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .accentMedical))
                .scaleEffect(1.2)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(titleForState)
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Text(messageForState)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.8))
                .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
        )
        .padding(.horizontal)
    }
    
    private var titleForState: String {
        switch state {
        case .loadingCache:
            return "Carregando dados locais"
        case .loadingServer:
            return "Sincronizando com o servidor"
        default:
            return "Carregando plantões"
        }
    }
    
    private var messageForState: String {
        switch state {
        case .loadingCache:
            return "Buscando plantões salvos no dispositivo..."
        case .loadingServer:
            return "Atualizando com os dados mais recentes..."
        default:
            return "Por favor, aguarde..."
        }
    }
}

#Preview {
    VStack(spacing: 20) {
        LoadingView(state: .loadingCache)
        LoadingView(state: .loadingServer)
        LoadingView(state: .idle)
    }
    .padding()
    .background(Color.gray.opacity(0.1))
}
